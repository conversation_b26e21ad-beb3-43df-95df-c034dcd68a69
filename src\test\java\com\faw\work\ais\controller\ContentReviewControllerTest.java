package com.faw.work.ais.controller;

import com.alibaba.fastjson.JSONObject;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.entity.domain.ContentRulePO;
import com.faw.work.ais.entity.request.*;
import com.faw.work.ais.service.ContentReviewService;
import com.faw.work.ais.service.ContentRuleService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ContentReviewControllerTest {

    @Mock
    private ContentRuleService contentRuleService;

    @Mock
    private ContentReviewService contentReviewService;

    @InjectMocks
    private ContentReviewController contentReviewController;

    @Test
    void commentScore_ValidRequest_ReturnsSuccess() {
        // 准备测试数据
        CommentRequest request = new CommentRequest();
        request.setTopic("testTopic");
        request.setContent("testContent");

        JSONObject expectedResponse = new JSONObject();
        expectedResponse.put("score", 95);
        when(contentReviewService.commentReview_v1(any())).thenReturn(expectedResponse);

        // 执行测试
        Response<JSONObject> response = contentReviewController.commentScore(request);

        // 验证结果
        assertEquals(HttpStatus.OK.value(), response.getCode());
        assertEquals("success", response.getMessage());
        assertEquals(95, response.getData().get("score"));
        verify(contentReviewService).commentReview_v1(request);
    }

    @Test
    void postReview_ValidRequest_ReturnsSuccess() {
        // 准备测试数据
        PostRequest request = new PostRequest();
        request.setContent("testContent");
        request.setPicUrls(Arrays.asList("url1", "url2"));

        JSONObject expectedResponse = new JSONObject();
        expectedResponse.put("score", 85);
        when(contentReviewService.postReview_v1(any())).thenReturn(expectedResponse);

        // 执行测试
        Response<JSONObject> response = contentReviewController.postReview(request);

        // 验证结果
        assertEquals(HttpStatus.OK.value(), response.getCode());
        assertEquals("success", response.getMessage());
        assertEquals(85, response.getData().get("score"));
        verify(contentReviewService).postReview_v1(request);
    }

    @Test
    void commentSummary_ValidRequest_ReturnsProcessing() {
        // 准备测试数据
        CommentSummaryRequest req1 = new CommentSummaryRequest();
        CommentSummaryRequest req2 = new CommentSummaryRequest();
        List<CommentSummaryRequest> requests = Arrays.asList(req1, req2);

        // 执行测试
        Response<String> response = contentReviewController.commentSummary(requests);

        // 验证结果
        assertEquals(HttpStatus.OK.value(), response.getCode());
        assertEquals("success", response.getMessage());
        assertEquals("处理中", response.getData());
        verify(contentReviewService).commentSummary(requests);
    }

    @Test
    void postReviewTruth_ValidRequest_ReturnsTruthScore() {
        // 准备测试数据
        PostRequest request = new PostRequest();
        request.setContent("factual content");

        JSONObject expectedResponse = new JSONObject();
        expectedResponse.put("truthScore", 90);
        when(contentReviewService.postReviewTruth(eq(request), eq(3))).thenReturn(expectedResponse);

        // 执行测试
        Response<JSONObject> response = contentReviewController.postReviewTruth(request);

        // 验证结果
        assertEquals(HttpStatus.OK.value(), response.getCode());
        assertEquals("success", response.getMessage());
        assertEquals(90, response.getData().get("truthScore"));
    }

    @Test
    void editRule_ValidRequest_ReturnsSuccess() {
        // 准备测试数据
        ContentRulePO rule = new ContentRulePO();
        rule.setId(1001L);

        when(contentReviewService.editRule(any())).thenReturn("Rule updated");

        // 执行测试
        Response<String> response = contentReviewController.editRule(rule);

        // 验证结果
        assertEquals(HttpStatus.OK.value(), response.getCode());
        assertEquals("success", response.getMessage());
        assertEquals("Rule updated", response.getData());
    }

    @Test
    void deleteRule_ValidId_ReturnsSuccess() {
        // 准备测试数据
        Long id = 1001L;
        when(contentReviewService.deleteRule(id)).thenReturn("Rule deleted");

        // 执行测试
        Response<String> response = contentReviewController.deleteRule(id);

        // 验证结果
        assertEquals(HttpStatus.OK.value(), response.getCode());
        assertEquals("success", response.getMessage());
        assertEquals("Rule deleted", response.getData());
    }
}