package com.faw.work.ais.entity.vo.ai;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "审核结果准确率返回队形")
@Data
public class ApproveRightRateVo {

    /**
     * 分子
     */
    @Schema(description = "分子（被除数）")
    private BigDecimal dividend;

    /**
     * 分母
     */
    @Schema(description = "分母（除数）")
    private BigDecimal divisor;

    /**
     * 商
     */
    @Schema(description = "商")
    private BigDecimal quotient;

}
