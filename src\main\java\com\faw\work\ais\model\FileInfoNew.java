package com.faw.work.ais.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
/**
* 文件信息表
* Created  by Mr.hp
* DateTime on 2025-01-13 13:51:57
* <AUTHOR> Mr.hp
* @Schema(title = "文件信息表", description = "文件信息表")
*/
@Data
@NoArgsConstructor
@Schema(description = "文件信息表")
public class FileInfoNew implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键ID
    * @Schema(description = "主键ID")
    */
    @Schema(description = "主键ID")
    private Long id;

    /**
    * trace_id
    * @Schema(description = "trace_id")
    */
    @Schema(description = "跟踪ID")
    private String traceId;

    /**
    * 文件路径（去掉验签）
    * @Schema(description = "文件路径（去掉验签）")
    */
    @Schema(description = "文件路径（去掉验签）")
    private String fileUrl;

    /**
    * 文件ID（cos的key）
    * @Schema(description = "文件ID（cos的key）")
    */
    @Schema(description = "文件ID（cos的key）")
    private String fileId;

    /**
    * 文件序号
    * @Schema(description = "文件序号")
    */
    @Schema(description = "文件序号")
    private Integer fileIndex;

    /**
    * 文件类型（0-文档，1-图片）
    * @Schema(description = "文件类型（0-文档，1-图片）")
    */
    @Schema(description = "文件类型（0-文档，1-图片）")
    private Integer fileType;

    /**
    * 创建时间
    * @Schema(description = "创建时间")
    */
    @Schema(description = "创建时间")
    private String createTime;


}
