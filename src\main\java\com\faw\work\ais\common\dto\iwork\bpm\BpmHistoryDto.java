package com.faw.work.ais.common.dto.iwork.bpm;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "查询bpm流程进度请求参数实体类dto")
public class BpmHistoryDto {

    @Schema(description = "应用码")
    private String appsource;

    @Schema(description = "租户ID")
    private String tenantLimitId;

    @Schema(description = "流程实例ID")
    private String procInstId;

    @Schema(description = "不返回流程预测,非必填")
    private Boolean returnNoPreviewNodes;

}
