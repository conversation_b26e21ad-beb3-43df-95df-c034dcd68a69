package com.faw.work.ais.aic.controller;

import cn.hutool.json.JSONUtil;
import com.faw.work.ais.aic.common.base.AiResult;
import com.faw.work.ais.aic.common.util.BaiLianUtils;
import com.faw.work.ais.aic.config.BaiLianAppConfig;
import com.faw.work.ais.aic.model.request.*;
import com.faw.work.ais.aic.model.response.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/bailian")
@Slf4j
@Tag(name = "百炼应用接口", description = "百炼应用接口")
public class BaiLianController {
    @Autowired
    private BaiLianAppConfig baiLianAppConfig;

    /**
     * 处理对话，包括情绪分析和标签提取
     *
     * @param request 包含文本和语音COS URL的请求
     * @return 处理结果
     */
    @Operation(summary = "门店语音质检结果总结", description = "[author:10200571]")
    @PostMapping("/getStoreVoiceQAReport")
    public AiResult<ZhiJianResult> getStoreVoiceQaReport(@RequestBody ServiceInspectionScoreInfoRes request) {
        ZhiJianResult result = BaiLianUtils.callForObject(
                baiLianAppConfig.getEmotionWorkspaceId(),
                baiLianAppConfig.getEmotionApiKey(),
                baiLianAppConfig.getZhiJianSummaryAppId(),
                JSONUtil.toJsonStr(request),
                ZhiJianResult.class);

        return AiResult.success(result);
    }




}
