package com.faw.work.ais.service.impl;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.aic.common.enums.EmbeddingModelTypeEnum;
import com.faw.work.ais.aic.service.EmbeddingService;
import com.faw.work.ais.common.util.VectorMathUtils;
import com.faw.work.ais.entity.dto.PracticePair;
import com.faw.work.ais.service.PracticeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PraciticeServiceImpl implements PracticeService {

    @Autowired
    private EmbeddingService embeddingService;


    @Override
    public Integer practiceScore(PracticePair practicePair) {
        String originalText = practicePair.getOriginalText();
        String voice = practicePair.getVoice();
//        return Response.success(contentReviewService.commentSummary(requests));
        float[] originalEmbedding;
        float[] voiceEmbedding;
        try {
            originalEmbedding = embeddingService.getEmbeddingByModel(EmbeddingModelTypeEnum.TEXT_EMBEDDING_V4, originalText);
            voiceEmbedding = embeddingService.getEmbeddingByModel(EmbeddingModelTypeEnum.TEXT_EMBEDDING_V4, voice);
        } catch (Exception e) {
            log.info("获取问题的向量表示失败: query={}", JSON.toJSONString(practicePair), e);
            // 如果QPS过高，使用v3模型兜底
            originalEmbedding = embeddingService.getEmbeddingByModel(EmbeddingModelTypeEnum.TEXT_EMBEDDING_V3, originalText);
            voiceEmbedding = embeddingService.getEmbeddingByModel(EmbeddingModelTypeEnum.TEXT_EMBEDDING_V3, voice);
        }
        // 转换 originalEmbedding
        double[] originalEmbeddingDouble = new double[originalEmbedding.length];
        for (int i = 0; i < originalEmbedding.length; i++) {
            originalEmbeddingDouble[i] = originalEmbedding[i];
        }

        // 转换 voiceEmbedding
        double[] voiceEmbeddingDouble = new double[voiceEmbedding.length];
        for (int i = 0; i < voiceEmbedding.length; i++) {
            voiceEmbeddingDouble[i] = voiceEmbedding[i];
        }
        // 计算相似度得分
        double originalScore = VectorMathUtils.cosineSimilarity(originalEmbeddingDouble, voiceEmbeddingDouble);
        log.info("originalScore:{}",originalScore);
        // 确保在 [0, 1] 范围内
        originalScore = Math.max(0, Math.min(1, originalScore));
        // 线性映射到 [0, 100]
        return (int) Math.round(originalScore * 100);
    }
}
