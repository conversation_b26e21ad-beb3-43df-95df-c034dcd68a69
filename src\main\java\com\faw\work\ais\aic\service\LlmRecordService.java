package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.model.request.AiRequest;
import com.faw.work.ais.aic.model.request.ProcessRequest;
import com.faw.work.ais.aic.model.request.RetryFailedMessagesRequest;
import com.faw.work.ais.aic.model.response.AiResponse;
import com.faw.work.ais.aic.model.response.MessageQueueCleanResponse;
import com.faw.work.ais.aic.model.response.RetryFailedMessagesResponse;

/**
 * <AUTHOR>
 */
public interface LlmRecordService {
    /**
     * 处理对话
     *
     * @param request 处理请求
     */
    void processConversation(ProcessRequest request);

    /**
     * 执行分片逻辑，将用户输入的对话内容进行分片并存储到数据库
     *
     * @param request 处理请求对象
     * @param requestId 请求ID
     */
    void performSlicing(ProcessRequest request, String requestId);

    /**
     * 重试失败的记录
     *
     * @param requestId 请求ID
     */
    void retryFailedRecords(String requestId);

    /**
     * 清理已完成的消息队列数据
     *
     * @return 清理结果
     */
    MessageQueueCleanResponse cleanCompletedMessages();

    /**
     * 处理分片数据
     *
     * @param requestId 请求ID
     * @return 处理结果
     */
    boolean processSlice(String requestId);

    /**
     * 处理分片数据（无事务版本，用于多线程环境）
     *
     * @param requestId 请求ID
     * @return 处理结果
     */
    boolean processSliceWithoutTransaction(String requestId);

    /**
     * 更新状态（新事务）
     *
     * @param id     记录ID
     * @param status 状态
     * @param remark 备注
     */
    void updateStatusTran(Long id, String status, String remark);

    /**
     * 清除请求数据
     *
     * @param messageId 消息id
     */
    void cleanRequestData(String messageId);

    /**
     * 测试聊天
     *
     * @param request 要求
     * @return {@link AiResponse }
     */
    AiResponse testChat(AiRequest request);

    /**
     * 重试所有失败的消息队列
     *
     * @param request 重试请求
     * @return 重试结果
     */
    RetryFailedMessagesResponse retryAllFailedMessages(RetryFailedMessagesRequest request);

}
