# 分片模型调用和合并逻辑说明

## 概述

根据需求完善了`performSlicing`方法，实现了基于大模型的分片标签分析和智能合并逻辑。

## 核心流程

### 1. 初步分片
```java
List<String> initialSlices = StrUtils.sliceConversation(conversationContent, "客户", 2, 1);
```
- 使用现有的StrUtils工具进行初步分片
- 参数调整为2，减少初始分片数量

### 2. 并发标签分析
```java
List<SliceInfo> sliceInfos = analyzeSliceTagsConcurrently(initialSlices);
```
- 使用专用线程池`sliceTagExecutor`（5个线程）
- 并发调用分片模型获取每个分片的标签
- 模型调用：`BaiLianUtils.callForObject(..., baiLianAppConfig.getEmotionSliceAppId(), ...)`

### 3. 智能合并
```java
List<String> mergedSlices = mergeSlicesBasedOnTags(sliceInfos);
```
- 基于标签交集和空标签规则进行合并
- 生成最终的分片列表

### 4. 创建数据库记录
```java
createLlmRecords(mergedSlices, request, requestId);
```
- 为情绪分析和产品需求分析创建LlmRecord

## 新增组件

### 1. SliceTagResponseDTO
```java
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SliceTagResponseDTO {
    private List<DemandTag> demandTags;
    
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DemandTag {
        private String level1;  // 一级标签
        private String level2;  // 二级标签
        private String level3;  // 三级标签
    }
}
```

### 2. SliceInfo内部类
```java
private static class SliceInfo {
    private final String content;                           // 分片内容
    private final List<SliceTagResponseDTO.DemandTag> tags; // 标签列表
    private final int originalIndex;                        // 原始索引
    
    // 核心方法
    public boolean hasEmptyTags()                          // 是否为空标签
    public boolean hasIntersectionWith(SliceInfo other)    // 是否有标签交集
}
```

## 核心算法

### 1. 并发标签分析 (analyzeSliceTagsConcurrently)

**功能**：并发调用分片模型获取每个分片的标签

**实现要点**：
- 使用`CompletableFuture`实现并发处理
- 专用线程池`sliceTagExecutor`（5个线程）
- 异常处理：分析失败的分片标记为空标签
- 结果按原始索引排序

**代码示例**：
```java
CompletableFuture<SliceInfo> future = CompletableFuture.supplyAsync(() -> {
    SliceTagResponseDTO result = BaiLianUtils.callForObject(
        baiLianAppConfig.getEmotionWorkspaceId(),
        baiLianAppConfig.getEmotionApiKey(),
        baiLianAppConfig.getEmotionSliceAppId(),
        slice,
        SliceTagResponseDTO.class);
    
    List<SliceTagResponseDTO.DemandTag> tags = (result != null && result.getDemandTags() != null) 
        ? result.getDemandTags() : new ArrayList<>();
    
    return new SliceInfo(slice, tags, index);
}, sliceTagExecutor);
```

### 2. 智能合并逻辑 (mergeSlicesBasedOnTags)

**合并规则**：
1. **空标签合并**：如果下一个分片的标签为空，直接与当前分片合并
2. **标签交集合并**：如果相邻两个分片的三级标签存在交集，执行合并

**算法流程**：
```java
for (int i = 0; i < sliceInfos.size(); i++) {
    SliceInfo slice = sliceInfos.get(i);
    
    if (currentSlice == null) {
        // 第一个分片
        currentSlice = slice;
        currentMergedContent.append(slice.getContent());
    } else {
        boolean shouldMerge = false;
        
        // 合并条件1：下一个分片标签为空
        if (slice.hasEmptyTags()) {
            shouldMerge = true;
        }
        // 合并条件2：三级标签存在交集
        else if (currentSlice.hasIntersectionWith(slice)) {
            shouldMerge = true;
        }
        
        if (shouldMerge) {
            // 执行合并
            currentMergedContent.append(" ").append(slice.getContent());
        } else {
            // 保存当前分片，开始新分片
            mergedSlices.add(currentMergedContent.toString().trim());
            currentSlice = slice;
            currentMergedContent = new StringBuilder(slice.getContent());
        }
    }
}
```

**标签交集判断**：
```java
public boolean hasIntersectionWith(SliceInfo other) {
    if (this.hasEmptyTags() || other.hasEmptyTags()) {
        return false;
    }
    
    // 检查三级标签是否有交集
    for (SliceTagResponseDTO.DemandTag thisTag : this.tags) {
        for (SliceTagResponseDTO.DemandTag otherTag : other.tags) {
            if (thisTag.getLevel3() != null && otherTag.getLevel3() != null 
                && thisTag.getLevel3().equals(otherTag.getLevel3())) {
                return true;
            }
        }
    }
    return false;
}
```

## 性能优化

### 1. 并发处理
- 使用专用线程池避免阻塞主线程
- 5个并发线程处理分片标签分析
- 异步等待所有任务完成

### 2. 异常处理
- 单个分片分析失败不影响整体流程
- 失败的分片标记为空标签，参与后续合并逻辑

### 3. 内存优化
- 使用StringBuilder进行字符串拼接
- 及时释放不需要的中间结果

## 日志记录

```java
log.info("初步分片完成，共{}个分片，开始调用分片模型进行标签分析", initialSlices.size());
log.info("分片[{}]标签分析完成，标签数量: {}", index, tags.size());
log.info("分片[{}]标签为空，与前一个分片合并", slice.getOriginalIndex());
log.info("分片[{}]与分片[{}]存在标签交集，执行合并", currentSlice.getOriginalIndex(), slice.getOriginalIndex());
log.info("分片合并完成，原始分片数: {}, 合并后分片数: {}", sliceInfos.size(), mergedSlices.size());
```

## 配置要求

确保在配置文件中设置了分片模型的AppId：
```yaml
dashscope:
  dms:
    emotion:
      slice-appId: "your-slice-app-id"
```

## 示例场景

**输入分片**：
- 分片A：标签 [{"level3": "整车性价比"}]
- 分片B：标签 [{"level3": "整车性价比"}] 
- 分片C：标签 []
- 分片D：标签 [{"level3": "现金优惠"}]

**合并结果**：
- 合并分片1：A + B + C（标签交集 + 空标签）
- 合并分片2：D（无交集，独立分片）

## 优势

1. **智能合并**：基于语义标签而非简单的文本规则
2. **高性能**：并发处理提高分析速度
3. **容错性**：单个分片失败不影响整体流程
4. **可扩展**：易于调整合并规则和标签分析逻辑
