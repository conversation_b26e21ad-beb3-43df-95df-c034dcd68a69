package com.faw.work.ais.aic.model.dto;

import java.util.ArrayList;
import java.util.List;

/**
 * 分片信息类
 * 
 * <AUTHOR>
 */
public class SliceInfo {
    
    /**
     * 分片内容
     */
    private final String content;
    
    /**
     * 标签列表
     */
    private final List<SliceTagResponseDTO.DemandTag> tags;
    
    /**
     * 原始索引
     */
    private final int originalIndex;

    /**
     * 构造函数
     *
     * @param content 分片内容
     * @param tags 标签列表
     * @param originalIndex 原始索引
     */
    public SliceInfo(String content, List<SliceTagResponseDTO.DemandTag> tags, int originalIndex) {
        this.content = content;
        this.tags = tags != null ? tags : new ArrayList<>();
        this.originalIndex = originalIndex;
    }

    /**
     * 获取分片内容
     *
     * @return 分片内容
     */
    public String getContent() {
        return content;
    }

    /**
     * 获取标签列表
     *
     * @return 标签列表
     */
    public List<SliceTagResponseDTO.DemandTag> getTags() {
        return tags;
    }

    /**
     * 获取原始索引
     *
     * @return 原始索引
     */
    public int getOriginalIndex() {
        return originalIndex;
    }

    /**
     * 判断是否为空标签
     *
     * @return true-空标签，false-非空标签
     */
    public boolean hasEmptyTags() {
        return tags.isEmpty();
    }

    /**
     * 判断与另一个分片是否有标签交集
     * 主要检查三级标签是否相同
     *
     * @param other 另一个分片信息
     * @return true-有交集，false-无交集
     */
    public boolean hasIntersectionWith(SliceInfo other) {
        if (this.hasEmptyTags() || other.hasEmptyTags()) {
            return false;
        }

        // 检查三级标签是否有交集
        for (SliceTagResponseDTO.DemandTag thisTag : this.tags) {
            for (SliceTagResponseDTO.DemandTag otherTag : other.tags) {
                if (thisTag.getLevel3() != null && otherTag.getLevel3() != null
                    && thisTag.getLevel3().equals(otherTag.getLevel3())) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public String toString() {
        return "SliceInfo{" +
                "content='" + (content != null ? content.substring(0, Math.min(50, content.length())) + "..." : "null") + '\'' +
                ", tagsCount=" + tags.size() +
                ", originalIndex=" + originalIndex +
                '}';
    }
}
