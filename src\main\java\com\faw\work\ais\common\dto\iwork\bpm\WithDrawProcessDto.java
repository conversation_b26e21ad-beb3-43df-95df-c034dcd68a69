package com.faw.work.ais.common.dto.iwork.bpm;



import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "撤回bpm流程请求参数实体类dto")
@EqualsAndHashCode
public class WithDrawProcessDto {

    @Schema(description = "应用码")
    private String appsource;

    @Schema(description = "租户ID")
    private String tenantLimitId;

    @Schema(description = "流程实例Id")
    @NotBlank(message = "流程实例id为空")
    private String procInstId;
}
