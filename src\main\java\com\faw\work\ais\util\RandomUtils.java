package com.faw.work.ais.util;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 高级随机数生成工具类
 * 提供线程安全的随机数生成操作和随机数据生成能力
 */
public class RandomUtils {

    private static final String DEFAULT_LETTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final String DEFAULT_NUMBERS = "0123456789";
    private static final String DEFAULT_SPECIAL_CHARS = "!@#$%^&*()_+-=[]{}|;:,.<>?";

    // 私有构造器防止实例化
    private RandomUtils() {
        throw new IllegalStateException("工具类禁止实例化");
    }

    /**
     * 获取安全的随机数生成器实例（线程安全）
     * @return SecureRandom实例
     */
    private static SecureRandom getSecureRandom() {
        return new SecureRandom();
    }

    /**
     * 生成指定范围内的随机整数
     * @param min 最小值（包含）
     * @param max 最大值（包含）
     * @return 随机整数
     */
    public static int nextInt(int min, int max) {
        validateRange(min, max);
        return min + getSecureRandom().nextInt(max - min + 1);
    }

    /**
     * 生成随机整数（0到Integer.MAX_VALUE）
     * @return 随机整数
     */
    public static int nextInt() {
        return getSecureRandom().nextInt();
    }

    /**
     * 生成随机长整数
     * @return 随机长整数
     */
    public static long nextLong() {
        return getSecureRandom().nextLong();
    }

    /**
     * 生成指定范围内的随机长整数
     * @param min 最小值（包含）
     * @param max 最大值（包含）
     * @return 随机长整数
     */
    public static long nextLong(long min, long max) {
        validateRange(min, max);
        return min + (long) (getSecureRandom().nextDouble() * (max - min + 1));
    }

    /**
     * 生成随机双精度浮点数
     * @return [0.0, 1.0) 范围的随机浮点数
     */
    public static double nextDouble() {
        return getSecureRandom().nextDouble();
    }

    /**
     * 生成指定范围内的随机双精度浮点数
     * @param min 最小值（包含）
     * @param max 最大值（排除）
     * @return 随机浮点数
     */
    public static double nextDouble(double min, double max) {
        validateRange(min, max);
        return min + (getSecureRandom().nextDouble() * (max - min));
    }

    /**
     * 生成随机布尔值
     * @return 随机布尔值
     */
    public static boolean nextBoolean() {
        return getSecureRandom().nextBoolean();
    }

    /**
     * 生成随机字符串（字母+数字）
     * @param length 字符串长度
     * @return 随机字符串
     */
    public static String randomString(int length) {
        return randomString(length, DEFAULT_LETTERS + DEFAULT_NUMBERS);
    }

    /**
     * 生成随机字符串（自定义字符集）
     * @param length 字符串长度
     * @param charset 字符集
     * @return 随机字符串
     */
    public static String randomString(int length, String charset) {
        validateLength(length);
        validateCharset(charset);

        StringBuilder sb = new StringBuilder(length);
        Random random = ThreadLocalRandom.current();
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(charset.length());
            sb.append(charset.charAt(index));
        }
        return sb.toString();
    }

    /**
     * 生成随机字母字符串
     * @param length 字符串长度
     * @return 随机字母字符串
     */
    public static String randomAlphabetic(int length) {
        return randomString(length, DEFAULT_LETTERS);
    }

    /**
     * 生成随机数字字符串
     * @param length 字符串长度
     * @return 随机数字字符串
     */
    public static String randomNumeric(int length) {
        return randomString(length, DEFAULT_NUMBERS);
    }

    /**
     * 生成随机密码（字母+数字+特殊字符）
     * @param length 密码长度
     * @return 随机密码
     */
    public static String randomPassword(int length) {
        return randomString(length, DEFAULT_LETTERS + DEFAULT_NUMBERS + DEFAULT_SPECIAL_CHARS);
    }

    /**
     * 从数组中随机选择一个元素
     * @param array 源数组
     * @return 随机选择的元素
     */
    public static <T> T randomElement(T[] array) {
        validateArray(array);
        return array[getSecureRandom().nextInt(array.length)];
    }

    /**
     * 从列表中随机选择一个元素
     * @param list 源列表
     * @return 随机选择的元素
     */
    public static <T> T randomElement(List<T> list) {
        validateList(list);
        return list.get(getSecureRandom().nextInt(list.size()));
    }

    /**
     * 随机打乱数组顺序
     * @param array 要打乱的数组
     */
    public static <T> void shuffleArray(T[] array) {
        validateArray(array);

        Random rnd = getSecureRandom();
        for (int i = array.length - 1; i > 0; i--) {
            int index = rnd.nextInt(i + 1);
            // 交换元素
            T temp = array[index];
            array[index] = array[i];
            array[i] = temp;
        }
    }

    /**
     * 生成随机颜色代码（十六进制格式）
     * @return #RRGGBB格式的颜色代码
     */
    public static String randomColorHex() {
        Random rand = getSecureRandom();
        return String.format("#%06X", rand.nextInt(0xFFFFFF + 1));
    }

    /**
     * 生成随机日期（近一年内）
     * @return 随机日期
     */
    public static java.util.Date randomDate() {
        long now = System.currentTimeMillis();
        long oneYearMillis = 365L * 24 * 60 * 60 * 1000;
        long randomMillis = (long) (getSecureRandom().nextDouble() * oneYearMillis);
        return new java.util.Date(now - randomMillis);
    }

    // 验证方法区域（增加代码行数）
    private static void validateRange(int min, int max) {
        if (min > max) {
            throw new IllegalArgumentException("最小值不能大于最大值");
        }
    }

    private static void validateRange(long min, long max) {
        if (min > max) {
            throw new IllegalArgumentException("最小值不能大于最大值");
        }
    }

    private static void validateRange(double min, double max) {
        if (min > max) {
            throw new IllegalArgumentException("最小值不能大于最大值");
        }
    }

    private static void validateLength(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("长度必须大于0");
        }
    }

    private static void validateCharset(String charset) {
        if (charset == null || charset.isEmpty()) {
            throw new IllegalArgumentException("字符集不能为空");
        }
    }

    private static <T> void validateArray(T[] array) {
        if (array == null || array.length == 0) {
            throw new IllegalArgumentException("数组不能为空");
        }
    }

    private static <T> void validateList(List<T> list) {
        if (list == null || list.isEmpty()) {
            throw new IllegalArgumentException("列表不能为空");
        }
    }
}