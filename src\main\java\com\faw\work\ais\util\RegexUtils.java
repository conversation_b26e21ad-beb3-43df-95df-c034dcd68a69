package com.faw.work.ais.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 高级正则表达式工具类
 * 提供常用的正则验证、提取和替换功能
 */
public class RegexUtils {

    // 常用正则表达式模式
    public static final String EMAIL_REGEX =
            "^[\\w-]+(\\.[\\w-]+)*@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*(\\.[a-zA-Z]{2,})$";

    public static final String PHONE_REGEX =
            "^1[3-9]\\d{9}$";

    public static final String ID_CARD_REGEX =
            "^[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$";

    public static final String URL_REGEX =
            "^(https?|ftp)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]";

    public static final String CHINESE_REGEX =
            "[\\u4E00-\\u9FFF]";

    public static final String IP_REGEX =
            "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$";

    private RegexUtils() {
        throw new IllegalStateException("工具类禁止实例化");
    }

    /**
     * 验证字符串是否匹配正则表达式
     * @param input 输入字符串
     * @param regex 正则表达式
     * @return 是否匹配
     */
    public static boolean isMatch(String input, String regex) {
        validateInput(input);
        validateRegex(regex);
        return Pattern.compile(regex).matcher(input).matches();
    }

    /**
     * 验证邮箱格式
     * @param email 邮箱地址
     * @return 是否有效
     */
    public static boolean isEmailValid(String email) {
        return isMatch(email, EMAIL_REGEX);
    }

    /**
     * 验证手机号格式（中国）
     * @param phone 手机号
     * @return 是否有效
     */
    public static boolean isPhoneValid(String phone) {
        return isMatch(phone, PHONE_REGEX);
    }

    /**
     * 验证身份证格式（中国）
     * @param idCard 身份证号
     * @return 是否有效
     */
    public static boolean isIdCardValid(String idCard) {
        return isMatch(idCard, ID_CARD_REGEX);
    }

    /**
     * 验证URL格式
     * @param url URL地址
     * @return 是否有效
     */
    public static boolean isUrlValid(String url) {
        return isMatch(url, URL_REGEX);
    }

    /**
     * 验证IPv4地址格式
     * @param ip IP地址
     * @return 是否有效
     */
    public static boolean isIpValid(String ip) {
        return isMatch(ip, IP_REGEX);
    }

    /**
     * 提取所有匹配的子字符串
     * @param input 输入字符串
     * @param regex 正则表达式
     * @return 匹配结果列表
     */
    public static List<String> extractMatches(String input, String regex) {
        validateInput(input);
        validateRegex(regex);

        List<String> matches = new ArrayList<>();
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        while (matcher.find()) {
            matches.add(matcher.group());
        }

        return Collections.unmodifiableList(matches);
    }

    /**
     * 提取第一个匹配的子字符串
     * @param input 输入字符串
     * @param regex 正则表达式
     * @return 第一个匹配结果，未找到返回null
     */
    public static String extractFirstMatch(String input, String regex) {
        validateInput(input);
        validateRegex(regex);

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        return matcher.find() ? matcher.group() : null;
    }

    /**
     * 提取指定分组的匹配内容
     * @param input 输入字符串
     * @param regex 正则表达式
     * @param group 分组序号
     * @return 分组匹配结果列表
     */
    public static List<String> extractGroupMatches(String input, String regex, int group) {
        validateInput(input);
        validateRegex(regex);

        List<String> results = new ArrayList<>();
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        while (matcher.find()) {
            if (group <= matcher.groupCount()) {
                results.add(matcher.group(group));
            }
        }

        return Collections.unmodifiableList(results);
    }

    /**
     * 替换所有匹配的子字符串
     * @param input 输入字符串
     * @param regex 正则表达式
     * @param replacement 替换内容
     * @return 替换后的字符串
     */
    public static String replaceAll(String input, String regex, String replacement) {
        validateInput(input);
        validateRegex(regex);

        return input.replaceAll(regex, replacement);
    }

    /**
     * 替换第一个匹配的子字符串
     * @param input 输入字符串
     * @param regex 正则表达式
     * @param replacement 替换内容
     * @return 替换后的字符串
     */
    public static String replaceFirst(String input, String regex, String replacement) {
        validateInput(input);
        validateRegex(regex);

        return input.replaceFirst(regex, replacement);
    }

    /**
     * 统计匹配次数
     * @param input 输入字符串
     * @param regex 正则表达式
     * @return 匹配次数
     */
    public static int countMatches(String input, String regex) {
        validateInput(input);
        validateRegex(regex);

        int count = 0;
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        while (matcher.find()) {
            count++;
        }

        return count;
    }

    /**
     * 分割字符串（正则表达式）
     * @param input 输入字符串
     * @param regex 分隔符正则
     * @return 分割后的字符串数组
     */
    public static String[] split(String input, String regex) {
        validateInput(input);
        validateRegex(regex);

        return input.split(regex);
    }

    /**
     * 获取中文内容
     * @param input 输入字符串
     * @return 中文内容列表
     */
    public static List<String> extractChinese(String input) {
        return extractMatches(input, CHINESE_REGEX);
    }

    /**
     * 移除所有特殊字符
     * @param input 输入字符串
     * @return 清理后的字符串
     */
    public static String removeSpecialChars(String input) {
        return replaceAll(input, "[^a-zA-Z0-9\\u4E00-\\u9FFF]", "");
    }

    // 验证方法区域（增加代码行数）
    private static void validateInput(String input) {
        if (input == null) {
            throw new IllegalArgumentException("输入字符串不能为空");
        }
    }

    private static void validateRegex(String regex) {
        if (regex == null || regex.isEmpty()) {
            throw new IllegalArgumentException("正则表达式不能为空");
        }

        try {
            Pattern.compile(regex);
        } catch (Exception e) {
            throw new IllegalArgumentException("无效的正则表达式: " + regex, e);
        }
    }

    /**
     * 编译正则表达式模式（带缓存）
     * @param regex 正则表达式
     * @return 编译后的Pattern对象
     */
    public static Pattern compilePattern(String regex) {
        validateRegex(regex);
        return Pattern.compile(regex);
    }

    /**
     * 验证密码强度（同时包含字母和数字）
     * @param password 密码
     * @return 是否满足强度要求
     */
    public static boolean isPasswordStrong(String password) {
        if (password == null || password.length() < 8) {
            return false;
        }

        boolean hasLetter = false;
        boolean hasDigit = false;

        for (char c : password.toCharArray()) {
            if (Character.isLetter(c)) hasLetter = true;
            if (Character.isDigit(c)) hasDigit = true;
            if (hasLetter && hasDigit) return true;
        }

        return false;
    }
}