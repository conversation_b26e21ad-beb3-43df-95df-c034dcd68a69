package com.faw.work.ais.entity.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "内容审核返回值实体类")
@Data
@Tag(name = "内容审核返回值实体类")

@NoArgsConstructor
public class ContentReviewResult {
    public ContentReviewResult(String type, int score) {
        this.type = type;
        this.score = score;
    }

    /**
     * 类型
     */
    @Schema(description = "类型")
    private String type;
    /**
     * 分数
     */
    @Schema(description = "分数")
    private int score;
    /**
     * 原因
     */
    @Schema(description = "原因")
    private String reason;

}
