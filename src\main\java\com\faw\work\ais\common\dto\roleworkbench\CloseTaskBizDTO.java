package com.faw.work.ais.common.dto.roleworkbench;

import com.faw.work.ais.common.enums.TaskBizEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "关闭任务 biz dto")
@Data
public class CloseTaskBizDTO {
    @Schema(description = "任务类型枚举值")
    private TaskBizEnum taskBizEnum;
    @Schema(description = "角色工作台触发任务后返回的当前任务流程code")
    private String taskInstanceCode;
}
