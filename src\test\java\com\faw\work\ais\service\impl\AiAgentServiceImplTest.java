package com.faw.work.ais.service.impl;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.aic.common.util.RedisService;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.MessageConstants;
import com.faw.work.ais.common.PromptConstants;
import com.faw.work.ais.common.dto.chat.AiAgentRequest;
import com.faw.work.ais.common.dto.chat.AiChatResponse;
import com.faw.work.ais.common.dto.chat.ToolCacheEntity;
import com.faw.work.ais.common.dto.chat.UserInfo;
import com.faw.work.ais.common.enums.chat.AnswerSourceEnum;
import com.faw.work.ais.common.enums.chat.ChatSceneEnum;
import com.faw.work.ais.common.enums.chat.GenderEnum;
import com.faw.work.ais.config.chat.ChatRedisMemory;
import com.faw.work.ais.feign.chat.AliYunFeignClient;
import com.faw.work.ais.service.tool.CommonService;
import com.faw.work.ais.service.tool.FunctionService;
import com.faw.work.ais.service.tool.KnowledgeService;
import com.faw.work.ais.service.tool.VehicleService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.model.ResultMetadata;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbacks;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AiAgentServiceImpl 单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class AiAgentServiceImplTest {

    @InjectMocks
    private AiAgentServiceImpl aiAgentService;

    @Mock
    private ChatClient chatClient;

    @Mock
    private RedisService redisService;

    @Mock
    private ChatRedisMemory chatMemory;

    @Mock
    private ChatClient universalClient;

    @Mock
    private ChatClient recommendClient;

    @Mock
    private CommonService commonService;

    @Mock
    private FunctionService functionService;

    @Mock
    private KnowledgeService knowledgeService;

    @Mock
    private VehicleService vehicleService;

    @Mock
    private AliYunFeignClient aliYunFeignClient;

    private AiAgentRequest baseRequest;

    @BeforeEach
    void setUp() {
        // 构建基础请求对象
        UserInfo userInfo = new UserInfo();
        userInfo.setGender(GenderEnum.FEMALE.getDesc());
        userInfo.setSeries("TestSeries");

        baseRequest = new AiAgentRequest();
        baseRequest.setSceneCode(ChatSceneEnum.SMALL_TALK.getCode());
        baseRequest.setQuestion("Hello");
        baseRequest.setCacheId("testCacheId");
        baseRequest.setChatId("testChatId");
        baseRequest.setUserInfo(userInfo);
    }

    /**
     * 测试 getAgentResponse 方法 - ANALYZE_PHOTO 场景
     */
    @Test
    void testGetAgentResponse_AnalyzePhoto() {
        // Given
        baseRequest.setSceneCode(ChatSceneEnum.ANALYZE_PHOTO.getCode());
        ArrayList<String> objects = new ArrayList<>();
        objects.add("http://test.com/image.jpg");
        baseRequest.setImageUrls(objects);

        // When
        String result = aiAgentService.getAgentResponse(baseRequest);
        
        // Then
        // 由于PhotoAnalysisService是局部new的，这里无法直接验证结果
        // 但可以验证没有抛出异常
        assertNotNull(result);
    }


    /**
     * 测试 getAgentResponse 方法 - 默认场景
     */
    @Test
    void testGetAgentResponse_Default() {
        // Given
        baseRequest.setSceneCode("UNKNOWN_SCENE");
        
        // When
        String result = aiAgentService.getAgentResponse(baseRequest);
        
        // Then
        assertEquals("未知智能体", result);
    }


}
