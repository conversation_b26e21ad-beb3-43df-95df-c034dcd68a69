package com.faw.work.ais.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * ugc服务相关配置
 */
@Configuration
@ConfigurationProperties(prefix = "ugcconfig")
@RefreshScope
@Data
public class UgcConfig {


    @Schema(description = "appKey")
    private String appKey;

    @Schema(description = "appSecret")
    private String appSecret;

    @Schema(description = "请求地址")
    private String hostUrl;

    @Schema(description = "解析token秘钥")
    private String jwtSecretKey;


}
