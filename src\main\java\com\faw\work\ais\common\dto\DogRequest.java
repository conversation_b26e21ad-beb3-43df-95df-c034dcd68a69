package com.faw.work.ais.common.dto;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.faw.work.ais.common.util.NullIfDecimalIntegerDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "养狗请求体")
public class DogRequest {

    @Schema(description = "通话记录")
    private String callText;

    @JsonDeserialize(using = NullIfDecimalIntegerDeserializer.class)
    @Schema(description = "版本")
    private Integer version;

    @Schema(description = "上次外呼时间")
    private String lastCallTime;
}
