package com.faw.work.ais.controller;

import com.alibaba.fastjson.JSONArray;
import com.dcp.common.rest.Result;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.common.dto.DogRequest;
import com.faw.work.ais.common.dto.DogResponse;
import com.faw.work.ais.entity.dto.OpsPromptGenerationDto;
import com.faw.work.ais.service.YangGouService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class YangGouControllerTest {

    @Mock
    private YangGouService yangGouService;

    @InjectMocks
    private YangGouController yangGouController;

    @Test
    void updateTemplate_ValidRequest_ReturnsSuccessMessage() {
        // 准备测试数据
        OpsPromptGenerationDto dto1 = new OpsPromptGenerationDto();
        OpsPromptGenerationDto dto2 = new OpsPromptGenerationDto();
        List<OpsPromptGenerationDto> request = Arrays.asList(dto1, dto2);

        // 执行测试
        Response<String> response = yangGouController.updateTemplate(request);

        // 验证结果
        assertEquals(HttpStatus.OK.value(), response.getCode());
        assertEquals("success", response.getMessage());
        assertEquals("接口已响应，任务后台处理中", response.getData());

        // 验证服务调用
        verify(yangGouService, times(1)).updateTemplate(request);
    }

    @Test
    void updateTemplate_EmptyList_ReturnsSuccessMessage() {
        // 准备空请求
        List<OpsPromptGenerationDto> request = Collections.emptyList();

        // 执行测试
        Response<String> response = yangGouController.updateTemplate(request);

        // 验证结果
        assertEquals(HttpStatus.OK.value(), response.getCode());
        assertEquals("接口已响应，任务后台处理中", response.getData());

        // 验证服务调用
        verify(yangGouService, times(1)).updateTemplate(request);
    }

    @Test
    void generate_ValidRequest_ReturnsDogResponse() {
        // 准备测试数据
        DogRequest request = new DogRequest();
        request.setCallText("Golden Retriever");

        DogResponse mockResponse = new DogResponse();
        mockResponse.setStrategy(new JSONArray());
        mockResponse.setVersion(-1);

        when(yangGouService.generate(any(DogRequest.class))).thenReturn(mockResponse);

        // 执行测试
        Result<DogResponse> result = yangGouController.generate(request);

        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals("Advanced obedience training", result.getData().getStrategy());
        assertEquals("Twice daily", result.getData().getVersion());

        // 验证服务调用
        verify(yangGouService, times(1)).generate(request);
    }

    @Test
    void generate_NullRequest_ReturnsDefaultResponse() {
        // 准备测试数据
        DogResponse mockResponse = new DogResponse();
        mockResponse.setStrategy(new JSONArray());

        when(yangGouService.generate(isNull())).thenReturn(mockResponse);

        // 执行测试
        Result<DogResponse> result = yangGouController.generate(null);

        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals("Default training", result.getData().getStrategy());
    }

    @Test
    void generate_ServiceException_ReturnsError() {
        // 准备测试数据
        DogRequest request = new DogRequest();

        when(yangGouService.generate(any()))
                .thenThrow(new RuntimeException("Service unavailable"));

        // 执行测试并验证异常
        Exception exception = assertThrows(RuntimeException.class, () -> {
            yangGouController.generate(request);
        });

        assertEquals("Service unavailable", exception.getMessage());
    }
}