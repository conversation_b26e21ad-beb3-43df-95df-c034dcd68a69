package com.faw.work.ais.entity.vo.ai;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "AI 任务 vo")
@Data
public class AiTaskVO {

    @Schema(description = "系统 ID")
    private String systemId;

    @Schema(description = "内容")
    private List<String> content;

    @Schema(description = "给定信息 json")
    private String givenInfoJson;

    @Schema(description = "给定信息 JSON DESC")
    private String givenInfoJsonDesc;

    @Schema(description = "")
    private String callbackUrl;

    @Schema(description = "网址")
    private String Url;

    @Schema(description = "回调类型")
    private String callbackType;

    @Schema(description = "任务类型")
    private String taskType;

    @Schema(description = "内容类型")
    private String contentType;

    @Schema(description = "业务 ID")
    private String bizId;

    @Schema(description = "业务类型")
    private String bizType;

    @Schema(description = "提示")
    private String prompt;

    @Schema(description = "版本")
    private String version;

    @Schema(description = "任务结果")
    private String taskResult;

}
