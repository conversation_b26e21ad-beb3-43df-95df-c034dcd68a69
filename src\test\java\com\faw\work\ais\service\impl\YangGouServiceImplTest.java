package com.faw.work.ais.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.faw.work.ais.aic.common.util.BaiLianUtils;
import com.faw.work.ais.aic.model.request.FaqDogKnowledgeInsertRequest;
import com.faw.work.ais.aic.model.request.FaqSearchByRobotRequest;
import com.faw.work.ais.aic.model.response.FaqKnowledgeResponse;
import com.faw.work.ais.aic.model.response.SimilarContentSearchResponse;
import com.faw.work.ais.aic.service.FaqKnowledgeService;
import com.faw.work.ais.aic.service.RagDocumentService;
import com.faw.work.ais.common.dto.CustomAttribute;
import com.faw.work.ais.common.dto.DogRequest;
import com.faw.work.ais.common.dto.DogResponse;
import com.faw.work.ais.common.dto.chat.AnalysisRequest;
import com.faw.work.ais.common.dto.chat.AnalysisResponse;
import com.faw.work.ais.config.YangGouConfig;
import com.faw.work.ais.entity.dto.OpsPromptGenerationDto;
import com.faw.work.ais.feign.AliClient;
import com.faw.work.ais.mapper.ais.PromptTemplateMapper;
import com.faw.work.ais.model.PromptTemplate;
import com.faw.work.ais.model.PromptUpdateItem;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * YangGouServiceImpl 单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class YangGouServiceImplTest {

    @InjectMocks
    private YangGouServiceImpl yangGouService;

    @Mock
    private YangGouConfig yangGouConfig;

    @Mock
    private PromptTemplateMapper promptTemplateMapper;

    @Mock
    private PromptService promptService;

    @Mock
    private AliClient aliClient;

    @Mock
    private FaqKnowledgeService faqKnowledgeService;

    @Mock
    private RagDocumentService ragDocumentService;

    @BeforeEach
    void setUp() {
        // 初始化Mock对象
        lenient().when(yangGouConfig.getWorkspaceId()).thenReturn("test-workspace-id");
        lenient().when(yangGouConfig.getApiKey()).thenReturn("test-api-key");
        lenient().when(yangGouConfig.getYanggouHighQualitySpeech2()).thenReturn("test-speech-app-id");
        lenient().when(yangGouConfig.getYanggouHighQualityPrompt()).thenReturn("test-prompt-app-id");
        lenient().when(yangGouConfig.getHighQualityKnowledge()).thenReturn("test-knowledge-app-id");
        lenient().when(yangGouConfig.getKehuhuaxiangAppId()).thenReturn("test-profile-app-id");
        lenient().when(yangGouConfig.getRobotId()).thenReturn("test-robot-id");
        lenient().when(yangGouConfig.getTopK()).thenReturn(5);
        lenient().when(yangGouConfig.getSimilarityThreshold()).thenReturn(0.8f);
        lenient().when(yangGouConfig.getEnv()).thenReturn("test");
    }

    /**
     * 测试 updateTemplate 方法正常流程
     */
    @Test
    void testUpdateTemplate_Normal() {
        // 准备测试数据
        List<OpsPromptGenerationDto> dtoList = new ArrayList<>();
        OpsPromptGenerationDto dto = new OpsPromptGenerationDto();
        dto.setCallContent("测试通话内容");
        dto.setSchedule("1");
        dto.setValid("1");
        dtoList.add(dto);

        // Mock AI服务调用
        String highQualitySpeech = "[{\"id\":\"1\",\"key\":\"客户画像分析\",\"value\":\"年轻首购型\"}]";
        mockStatic(BaiLianUtils.class);
        when(BaiLianUtils.callForObject(anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(highQualitySpeech, // 第一次调用返回优质话术
                        "{\"id\":\"1\",\"value\":\"优化后的话术\"}"); // 第二次调用返回优化后的话术

        // Mock 数据库查询
        PromptTemplate oldTemplate = new PromptTemplate();
        oldTemplate.setPrompt("{\"年轻首购型\":[{\"id\":\"1\",\"value\":\"旧话术\"}]}");
        oldTemplate.setVersion(1);
        when(promptTemplateMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(oldTemplate);

        // 执行测试
        yangGouService.updateTemplate(dtoList);

        // 验证结果
        verify(promptTemplateMapper, times(1)).insert(any(PromptTemplate.class));
        ArgumentCaptor<FaqDogKnowledgeInsertRequest> knowledgeCaptor = ArgumentCaptor.forClass(FaqDogKnowledgeInsertRequest.class);
        verify(faqKnowledgeService, times(1)).dogKnowledgeInsert(knowledgeCaptor.capture());
        
        FaqDogKnowledgeInsertRequest capturedRequest = knowledgeCaptor.getValue();
        assertEquals("test-robot-id", capturedRequest.getRobotId());
    }

    /**
     * 测试 updateTemplate 方法处理空列表
     */
    @Test
    void testUpdateTemplate_EmptyList() {
        // 执行测试
        yangGouService.updateTemplate(new ArrayList<>());

        // 验证没有调用AI服务
        verify(promptTemplateMapper, times(1)).selectOne(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试 getLatestPromptTemplate 方法正常流程
     */
    @Test
    void testGetLatestPromptTemplate_Normal() {
        // 准备测试数据
        Integer version = 1;
        String customerProfile = "年轻首购型";

        PromptTemplate template = new PromptTemplate();
        template.setPrompt("{\"年轻首购型\":[{\"id\":\"greeting\",\"value\":\"您好\"}]}");
        template.setVersion(1);

        when(promptTemplateMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(template);
        when(promptService.getPromptText()).thenReturn("欢迎语：{greeting}");

        // 执行测试
        PromptTemplate result = yangGouService.getLatestPromptTemplate(version, customerProfile);

        // 验证结果
        assertNotNull(result);
        assertEquals("欢迎语：您好", result.getPrompt());
        assertEquals(1, result.getVersion());
    }

    /**
     * 测试 generate 方法正常流程
     */
    @Test
    void testGenerate_Normal() {
        // 准备测试数据
        DogRequest dogRequest = new DogRequest();
        dogRequest.setCallText("客户询问车辆信息");

        // Mock 客户画像AI服务
        CustomAttribute customAttribute = new CustomAttribute();
        customAttribute.setCustomerProfile("年轻首购型");
        customAttribute.setCustomerTraits("预算有限，关注性价比");
        when(BaiLianUtils.callForObject(anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(customAttribute);

        // Mock FAQ知识库服务
        FaqKnowledgeResponse faqResponse = new FaqKnowledgeResponse();
        faqResponse.setAnswer("这是破冰话术示例");
        when(faqKnowledgeService.searchByRobotId(any(FaqSearchByRobotRequest.class)))
                .thenReturn(Arrays.asList(faqResponse));

        // Mock 文档服务
        SimilarContentSearchResponse docResponse = new SimilarContentSearchResponse();
        docResponse.setDocumentContent("这是Excel导入的知识");
        when(ragDocumentService.searchSimilarContentNew(any())).thenReturn(Arrays.asList(docResponse));

        // Mock 提示模板
        PromptTemplate template = new PromptTemplate();
        template.setPrompt("根据以下信息生成策略");
        template.setVersion(1);
        when(promptTemplateMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(template);
        when(promptService.getPromptText()).thenReturn("系统提示模板");


        // 执行测试
        DogResponse result = yangGouService.generate(dogRequest);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getStrategy());
        assertEquals(1, result.getStrategy().size());
        assertEquals(1, result.getVersion());
    }

    /**
     * 测试 generate 方法处理AI服务返回空
     */
    @Test
    void testGenerate_AiReturnsNull() {
        // 准备测试数据
        DogRequest dogRequest = new DogRequest();
        dogRequest.setCallText("客户询问车辆信息");

        // Mock 客户画像AI服务返回空
        when(BaiLianUtils.callForObject(anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(null);

        // Mock 提示模板
        PromptTemplate template = new PromptTemplate();
        template.setPrompt("根据以下信息生成策略");
        template.setVersion(1);
        when(promptTemplateMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(template);
        when(promptService.getPromptText()).thenReturn("系统提示模板");

        // Mock 阿里AI客户端返回空
        when(aliClient.analyze(any(AnalysisRequest.class), anyString())).thenReturn(null);

        // 执行测试
        DogResponse result = yangGouService.generate(dogRequest);

        // 验证结果
        assertNotNull(result);
        assertNull(result.getStrategy());
        assertEquals(1, result.getVersion());
    }

    /**
     * 测试 generateNewPrompt2 方法
     */
    @Test
    void testGenerateNewPrompt2() {
        // 准备测试数据
        String oldConfig = "{\"年轻首购型\":[{\"id\":\"1\",\"value\":\"旧值\"}]}";
        Map<String, List<PromptUpdateItem>> newConfig = new HashMap<>();
        PromptUpdateItem item = new PromptUpdateItem();
        item.setId("1");
        item.setValue("新值");
        newConfig.put("年轻首购型", Arrays.asList(item));

        // 执行测试
    }

    /**
     * 测试 generateNewPrompt2 方法处理空配置
     */
    @Test
    void testGenerateNewPrompt2_EmptyConfig() {
        // 准备测试数据
        Map<String, List<PromptUpdateItem>> newConfig = new HashMap<>();
        PromptUpdateItem item = new PromptUpdateItem();
        item.setId("1");
        item.setValue("新值");
        newConfig.put("年轻首购型", Arrays.asList(item));

    }
}
