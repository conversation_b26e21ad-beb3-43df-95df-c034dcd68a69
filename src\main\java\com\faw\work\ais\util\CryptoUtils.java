package com.faw.work.ais.util;

import javax.crypto.*;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.KeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 加密解密工具类
 * 支持多种加密算法：AES, RSA, SHA, HMAC, PBKDF2等
 */
public class CryptoUtils {

    /* ------------------------- 哈希算法 ------------------------- */

    /**
     * 计算MD5哈希值
     * @param input 输入字符串
     * @return 16进制格式的MD5哈希值
     */
    public static String md5(String input) {
        return hash(input, "MD5");
    }

    /**
     * 计算SHA-256哈希值
     * @param input 输入字符串
     * @return 16进制格式的SHA-256哈希值
     */
    public static String sha256(String input) {
        return hash(input, "SHA-256");
    }

    /**
     * 计算SHA-512哈希值
     * @param input 输入字符串
     * @return 16进制格式的SHA-512哈希值
     */
    public static String sha512(String input) {
        return hash(input, "SHA-512");
    }

    private static String hash(String input, String algorithm) {
        try {
            MessageDigest digest = MessageDigest.getInstance(algorithm);
            byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new CryptoException("哈希计算失败: " + algorithm, e);
        }
    }

    /* ------------------------- 对称加密（AES） ------------------------- */

    /**
     * AES加密（GCM模式）
     * @param plainText 明文
     * @param password 密码
     * @return Base64编码的加密结果字符串（格式：iv:tag:ciphertext）
     */
    public static String aesEncrypt(String plainText, String password) {
        try {
            // 生成随机盐和IV
            byte[] salt = generateRandomBytes(16);
            byte[] iv = generateRandomBytes(12);

            // 从密码派生密钥
            SecretKey key = deriveAesKey(password, salt);

            // 加密
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            cipher.init(Cipher.ENCRYPT_MODE, key, new GCMParameterSpec(128, iv));
            byte[] cipherText = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));

            // 组合结果：salt + iv + 密文
            byte[] combined = new byte[salt.length + iv.length + cipherText.length];
            System.arraycopy(salt, 0, combined, 0, salt.length);
            System.arraycopy(iv, 0, combined, salt.length, iv.length);
            System.arraycopy(cipherText, 0, combined, salt.length + iv.length, cipherText.length);

            return Base64.getEncoder().encodeToString(combined);
        } catch (Exception e) {
            throw new CryptoException("AES加密失败", e);
        }
    }

    /**
     * AES解密（GCM模式）
     * @param encryptedText Base64编码的加密字符串
     * @param password 密码
     * @return 解密后的明文
     */
    public static String aesDecrypt(String encryptedText, String password) {
        try {
            // 解析Base64
            byte[] combined = Base64.getDecoder().decode(encryptedText);

            // 提取salt (16字节), IV (12字节), 密文
            byte[] salt = new byte[16];
            byte[] iv = new byte[12];
            byte[] cipherText = new byte[combined.length - salt.length - iv.length];

            System.arraycopy(combined, 0, salt, 0, salt.length);
            System.arraycopy(combined, salt.length, iv, 0, iv.length);
            System.arraycopy(combined, salt.length + iv.length, cipherText, 0, cipherText.length);

            // 从密码派生密钥
            SecretKey key = deriveAesKey(password, salt);

            // 解密
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            cipher.init(Cipher.DECRYPT_MODE, key, new GCMParameterSpec(128, iv));
            byte[] plainText = cipher.doFinal(cipherText);

            return new String(plainText, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new CryptoException("AES解密失败", e);
        }
    }

    private static SecretKey deriveAesKey(String password, byte[] salt) throws Exception {
        // 使用PBKDF2派生密钥
        KeySpec spec = new PBEKeySpec(password.toCharArray(), salt, 65536, 256);
        SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
        byte[] keyBytes = factory.generateSecret(spec).getEncoded();
        return new SecretKeySpec(keyBytes, "AES");
    }

    /* ------------------------- 非对称加密（RSA） ------------------------- */

    /**
     * 生成RSA密钥对
     * @return 包含公钥和私钥的Map
     */
    public static Map<String, String> generateRsaKeyPair() {
        try {
            KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
            keyGen.initialize(2048);
            KeyPair keyPair = keyGen.generateKeyPair();

            Map<String, String> keys = new HashMap<>();
            keys.put("public", Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded()));
            keys.put("private", Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded()));
            return keys;
        } catch (NoSuchAlgorithmException e) {
            throw new CryptoException("RSA密钥对生成失败", e);
        }
    }

    /**
     * RSA公钥加密
     * @param plainText 明文
     * @param publicKey Base64编码的公钥
     * @return Base64编码的加密结果
     */
    public static String rsaEncrypt(String plainText, String publicKey) {
        try {
            PublicKey key = getPublicKey(publicKey);
            Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-256AndMGF1Padding");
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byte[] cipherText = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(cipherText);
        } catch (Exception e) {
            throw new CryptoException("RSA加密失败", e);
        }
    }

    /**
     * RSA私钥解密
     * @param encryptedText Base64编码的密文
     * @param privateKey Base64编码的私钥
     * @return 解密后的明文
     */
    public static String rsaDecrypt(String encryptedText, String privateKey) {
        try {
            PrivateKey key = getPrivateKey(privateKey);
            Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-256AndMGF1Padding");
            cipher.init(Cipher.DECRYPT_MODE, key);
            byte[] cipherText = Base64.getDecoder().decode(encryptedText);
            byte[] plainText = cipher.doFinal(cipherText);
            return new String(plainText, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new CryptoException("RSA解密失败", e);
        }
    }

    private static PublicKey getPublicKey(String base64PublicKey) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(base64PublicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(keySpec);
    }

    private static PrivateKey getPrivateKey(String base64PrivateKey) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(base64PrivateKey);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(keySpec);
    }

    /* ------------------------- 消息认证码（HMAC） ------------------------- */

    /**
     * 计算HMAC-SHA256
     * @param data 原始数据
     * @param key 密钥
     * @return Base64编码的HMAC值
     */
    public static String hmacSha256(String data, String key) {
        try {
            Mac hmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            hmac.init(secretKey);
            byte[] hmacBytes = hmac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hmacBytes);
        } catch (Exception e) {
            throw new CryptoException("HMAC计算失败", e);
        }
    }

    /* ------------------------- 实用方法 ------------------------- */

    /**
     * 生成安全的随机字节数组
     * @param length 字节数组长度
     * @return 随机字节数组
     */
    public static byte[] generateRandomBytes(int length) {
        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[length];
        random.nextBytes(bytes);
        return bytes;
    }

    /**
     * 字节数组转16进制字符串
     * @param bytes 字节数组
     * @return 16进制字符串
     */
    public static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    /**
     * 16进制字符串转字节数组
     * @param hex 16进制字符串
     * @return 字节数组
     */
    public static byte[] hexToBytes(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }

    /**
     * Base64编码
     * @param input 输入字符串
     * @return Base64编码结果
     */
    public static String base64Encode(String input) {
        return Base64.getEncoder().encodeToString(input.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * Base64解码
     * @param input Base64编码字符串
     * @return 解码后的字符串
     */
    public static String base64Decode(String input) {
        byte[] decoded = Base64.getDecoder().decode(input);
        return new String(decoded, StandardCharsets.UTF_8);
    }

    /* ------------------------- 自定义异常 ------------------------- */

    public static class CryptoException extends RuntimeException {
        public CryptoException(String message) {
            super(message);
        }

        public CryptoException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
