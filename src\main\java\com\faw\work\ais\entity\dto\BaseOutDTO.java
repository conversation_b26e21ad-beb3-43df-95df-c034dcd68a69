package com.faw.work.ais.entity.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "基出 DTO")
@Data
@Tag(name = "通用返回DTO")

public class BaseOutDTO<T> {

    @Schema(description = "成功")
    private String success;
    @Schema(description = "数据")
    private String data;
    @Schema(description = "消息")
    private String message;
    @Schema(description = "业务 ID")
    private String bizId;
}
