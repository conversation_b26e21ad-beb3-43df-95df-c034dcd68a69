package com.faw.work.ais.entity.dto.ai;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;


/**
 * aiTask入参
 * <AUTHOR>
 * @since 2024/4/3
 */
@Schema(description = "aiTask入参")
@Data
public class AiTaskDTO {

    @Schema(description = "系统 ID")
    @NotNull
    private String systemId;

    @Schema(description = "ai审核模型id")
    private Long auditModelSceneId;

    @Schema(description = "内容")
    private List<BeCheckFileDTO> contents;

    @Schema(description = "给定信息 json")
    @NotEmpty
    private String givenInfoJson;

    @Schema(description = "给定信息 JSON DESC")
    @NotEmpty
    private String givenInfoJsonDesc;

    @Schema(description = "回调 URL")
    private String callbackUrl;

    @Schema(description = "回调类型")
    private String callbackType;

    @Schema(description = "任务类型")
    @NotEmpty
    private String taskType;

    @Schema(description = "业务 ID")
    @NotEmpty
    private String bizId;

    @Schema(description = "业务类型")
    private String bizType;

    @Schema(description = "回调自定义参数")
    private String callBackCustomParam;

    @Schema(description = "版本")
    private String version;

    @Schema(description = "跟踪 ID")
    @NotNull
    private String traceId;

    @Schema(description = "测试")
    private String test;

    @Schema(description = "批次 ID")
    private String batchId;
}
