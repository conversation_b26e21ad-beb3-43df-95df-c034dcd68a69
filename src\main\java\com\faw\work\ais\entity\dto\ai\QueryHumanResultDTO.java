package com.faw.work.ais.entity.dto.ai;

import com.faw.work.ais.common.PageRequest;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 运营中心人工审核列表请求对象
 * <AUTHOR>
 * @since 2025/1/15
 */
@Schema(description = "运营中心人工审核列表请求对象")
@Data
public class QueryHumanResultDTO extends PageRequest {

    @Schema(description = "系统id")
    private String systemId;

    @Schema(description = "AI审核开始日期")
    private String aiCheckTimeStart;

    @Schema(description = "AI审核结束日期")
    private String aiCheckTimeEnd;

    @Schema(description = "人工审核开始日期")
    private String humanCheckTimeStart;

    @Schema(description = "人工审核结束日期")
    private String humanCheckTimeEnd;

    @Schema(description = "单据准确率选中标识；0-未选中；1-选中；和规则准确率互斥，默认为0")
    private String billRightRateFlag;

    @Schema(description = "规则准确率选中标识；0-未选中；1-选中；和规则准确率互斥，默认为0")
    private String ruleCheckRateFlag;

    @Schema(description = "单据id")
    private String batchId;

    @Schema(description = "请求的唯一id")
    private String traceId;

    @Schema(description = "规则id")
    private String taskType;

}
