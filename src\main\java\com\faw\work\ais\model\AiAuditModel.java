package com.faw.work.ais.model;

import com.faw.work.ais.model.base.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * ai审核模型配置表
 * Created  by Mr.hp
 * DateTime on 2025-05-08 10:36:35
 *
 * <AUTHOR> Mr.hp
 * @Schema(title = "ai审核模型配置表", description = "ai审核模型配置表")
 */
@Data
@NoArgsConstructor
@Schema(description = "人工智能ai审核模型配置表")
public class AiAuditModel extends PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private Long id;

    /**
     * 模型名称
     */
    @Schema(description = "模型名称")
    private String auditModelName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private String createTime;

    /**
     * 创建人编码
     */
    @Schema(description = "创建人编码")
    private String createUserCode;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private String updateTime;

    /**
     * 更新人编码
     *
     * @Schema(description = "更新人编码")
     */
    @Schema(description = "更新人编码")
    private String updateUserCode;

    /**
     * 更新人姓名
     *
     * @Schema(description = "更新人姓名")
     */
    @Schema(description = "更新人姓名")
    private String updateUserName;

    @Schema(description = "人工智能ai技术模型配置表")
    List<AiAuditModelTechnology> aiAuditModelTechnologyList;
    @Schema(description = "人工智能ai审核点关系表")
    List<AiAuditModelPoint> aiAuditModelPointList;
    @Schema(description = "人工智能ai审核场景关系表")
    List<AiAuditModelScene> aiAuditModelSceneList;


}
