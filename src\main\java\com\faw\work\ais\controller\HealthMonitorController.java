package com.faw.work.ais.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "healthMonitor", description = "运行状况监视器控制器")
@Slf4j
@RestController("HealthMonitorController")
@RequestMapping("/health")
public class HealthMonitorController {

    @Operation(summary = "运行状况监视器控制器", description = "[author:10236535]")
    @GetMapping("/empty")
    public void dynamicUrlTest(){
        log.info("--------------empty接口开始执行--------------------");

        log.info("--------------empty接口执行完成--------------------");
    }
}
