package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Milvus集合迁移响应结果
 * <AUTHOR>
 */
@Data
@Schema(description = "<PERSON>l<PERSON><PERSON>集合迁移响应结果 [author:10200571]")
public class MigrateCollectionResponse {

    /**
     * 总记录数
     */
    @Schema(description = "总记录数")
    private Long totalCount;

    /**
     * 已迁移记录数
     */
    @Schema(description = "已迁移记录数")
    private Long migratedCount;

    /**
     * 迁移状态
     */
    @Schema(description = "迁移状态")
    private String status;

    /**
     * 迁移信息
     */
    @Schema(description = "迁移信息")
    private String message;
}
