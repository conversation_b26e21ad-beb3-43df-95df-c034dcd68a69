package com.faw.work.ais.aic.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatOptions;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.faw.work.ais.aic.common.enums.LLMBizTypeEnum;
import com.faw.work.ais.aic.common.enums.MessageTypeEnum;
import com.faw.work.ais.aic.common.mq.MessageProducer;
import com.faw.work.ais.aic.common.util.BaiLianUtils;
import com.faw.work.ais.aic.common.util.DateUtils;
import com.faw.work.ais.aic.common.util.RedisService;
import com.faw.work.ais.aic.common.util.StrUtils;
import com.faw.work.ais.aic.config.BaiLianAppConfig;
import com.faw.work.ais.aic.feign.DgwOpenApiFeignClient;
import com.faw.work.ais.aic.feign.dto.DgwResult;
import com.faw.work.ais.aic.mapper.llm.LlmRecordMapper;
import com.faw.work.ais.aic.model.domain.LlmRecord;
import com.faw.work.ais.aic.model.dto.EmotionResponseDTO;
import com.faw.work.ais.aic.model.dto.TagAnalysisDTO;
import com.faw.work.ais.aic.model.dto.TopicSummaryDTO;
import com.faw.work.ais.aic.model.request.AiRequest;
import com.faw.work.ais.aic.model.request.ProcessRequest;
import com.faw.work.ais.aic.model.response.AiResponse;
import com.faw.work.ais.aic.model.response.DmsEmotionResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LlmRecordServiceV2Impl 单元测试
 */
@SpringBootTest
public class LlmRecordServiceV2ImplTest {

    @InjectMocks
    private LlmRecordServiceV2Impl llmRecordService;

    @Mock
    private DgwOpenApiFeignClient dgwFeignClient;

    @Mock
    private ChatClient chatClient;

    @Mock
    private LlmRecordMapper llmRecordMapper;

    @Mock
    private MessageProducer messageProducer;

    @Mock
    private BaiLianAppConfig baiLianAppConfig;

    @Mock
    private LlmRecordServiceV2Impl self;

    @Mock
    private Executor dmsEmotionExecutor;

    @Mock
    private RedisService redisService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    /**
     * 测试 processConversationAbTest 方法正常情况
     */
    @Test
    public void testProcessConversationAbTest_Normal() {
        // Given
        ProcessRequest request = new ProcessRequest();
        request.setRequestId("test-request-id");
        request.setUserInput("客户: 你好\n销售: 你好，请问有什么可以帮助你的吗？\n客户: 我想了解一下你们的产品");
        request.setAudioUrl("http://test.com/audio.mp3");

        when(llmRecordMapper.selectByRequestId(anyString(), any(), any())).thenReturn(new ArrayList<>());
        when(StrUtils.sliceConversation(anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(List.of("客户: 你好", "客户: 我想了解一下你们的产品"));

        // Mock DateUtils
        try (MockedStatic<DateUtils> mockedDateUtils = mockStatic(DateUtils.class)) {
            mockedDateUtils.when(DateUtils::getCurrentDateTimeString).thenReturn("2023-01-01 12:00:00");

            // When
            llmRecordService.processConversationAbTest(request);

            // Then
            verify(llmRecordMapper, times(2)).selectByRequestId(anyString(), any(), any());
            verify(self, times(2)).saveBatch(anyList());
            verify(redisService).set(anyString(), anyString(), anyInt());
            verify(messageProducer).sendMessage(anyString(), anyString());
        }
    }

    /**
     * 测试 processConversationAbTest 方法 - 请求ID已存在
     */
    @Test
    public void testProcessConversationAbTest_RequestIdExists() {
        // Given
        ProcessRequest request = new ProcessRequest();
        request.setRequestId("test-request-id");
        List<LlmRecord> existingRecords = new ArrayList<>();
        existingRecords.add(new LlmRecord());
        when(llmRecordMapper.selectByRequestId(anyString(), any(), any())).thenReturn(existingRecords);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            llmRecordService.processConversationAbTest(request);
        });
    }

    /**
     * 测试 processSliceAbTest 方法正常情况
     */
    @Test
    public void testProcessSliceAbTest_Normal() {
        // Given
        String requestId = "test-request-id";
        List<LlmRecord> slices = new ArrayList<>();
        LlmRecord emotionSlice = new LlmRecord();
        emotionSlice.setId(1L);
        emotionSlice.setBizType(LLMBizTypeEnum.DMS_EMOTION.getCode());
        emotionSlice.setRequestId(requestId);
        emotionSlice.setUserInput("客户情绪内容");
        slices.add(emotionSlice);

        LlmRecord productSlice = new LlmRecord();
        productSlice.setId(2L);
        productSlice.setBizType(LLMBizTypeEnum.DMS_PRODUCT.getCode());
        productSlice.setRequestId(requestId);
        productSlice.setUserInput("客户产品需求内容");
        slices.add(productSlice);

        when(llmRecordMapper.selectByRequestId(anyString(), anyString(), any())).thenReturn(slices);

        // Mock emotion analysis result
        EmotionResponseDTO emotionResponse = new EmotionResponseDTO();
        emotionResponse.setStart("积极");
        emotionResponse.setEnd("满意");
        when(redisService.get(anyString())).thenReturn(JSONUtil.toJsonStr(new AiRequest()));
        
        // Mock testChat response
        try (MockedConstruction<LlmRecordServiceV2Impl> mockedService = mockConstruction(LlmRecordServiceV2Impl.class, (mock, context) -> {
            AiResponse aiResponse = AiResponse.builder().content(JSONUtil.toJsonStr(emotionResponse)).build();
            when(mock.testChat(any())).thenReturn(aiResponse);
        })) {
            
            // Mock tag extraction result
            TagAnalysisDTO tagResult = new TagAnalysisDTO();
            List<TagAnalysisDTO.TopicSummary> summaries = new ArrayList<>();
            TagAnalysisDTO.TopicSummary summary = new TagAnalysisDTO.TopicSummary();
            summary.setQuestion("产品问题");
            summary.setAnswer("产品答案");
            summaries.add(summary);
            tagResult.setCustomerQuestionSummaries(summaries);
            try (MockedStatic<BaiLianUtils> mockedBaiLian = mockStatic(BaiLianUtils.class)) {
                mockedBaiLian.when(() -> BaiLianUtils.callForObject(anyString(), anyString(), anyString(), anyString(), any()))
                        .thenReturn(tagResult);
                
                // Mock topic summary
                TopicSummaryDTO topicSummary = new TopicSummaryDTO();
                topicSummary.setTotalEmotion("积极");
                mockedBaiLian.when(() -> BaiLianUtils.callForObject(anyString(), anyString(), anyString(), anyString(), any()))
                        .thenReturn(topicSummary);
                
                // Mock finish lists
                List<LlmRecord> finishEmotionList = new ArrayList<>();
                LlmRecord finishEmotion = new LlmRecord();
                finishEmotion.setLlmOutput(JSONUtil.toJsonStr(emotionResponse));
                finishEmotion.setUserInput("客户情绪内容");
                finishEmotionList.add(finishEmotion);
                when(llmRecordMapper.selectByRequestId(anyString(), anyString(), eq(LLMBizTypeEnum.DMS_EMOTION.getCode())))
                        .thenReturn(finishEmotionList);
                
                List<LlmRecord> finishTagList = new ArrayList<>();
                LlmRecord finishTag = new LlmRecord();
                finishTag.setLlmOutput(JSONUtil.toJsonStr(tagResult));
                finishTagList.add(finishTag);
                when(llmRecordMapper.selectByRequestId(anyString(), anyString(), eq(LLMBizTypeEnum.DMS_PRODUCT.getCode())))
                        .thenReturn(finishTagList);
                
                // Mock callback
                DgwResult dgwResult = new DgwResult();
                when(dgwFeignClient.callBackDmsEmotion(any(DmsEmotionResponse.class))).thenReturn(dgwResult);
                
                // When
                llmRecordService.processSliceAbTest(requestId);
                
                // Then
                verify(dgwFeignClient).callBackDmsEmotion(any(DmsEmotionResponse.class));
            }
        }
    }

    /**
     * 测试 performEmotionAnalysisAbTest 方法正常情况
     */
    @Test
    public void testPerformEmotionAnalysisAbTest_Normal() {
        // Given
        LlmRecord emotion = new LlmRecord();
        emotion.setId(1L);
        emotion.setRequestId("test-request-id");
        emotion.setUserInput("客户情绪内容");

        AiRequest aiRequest = new AiRequest();
        aiRequest.setModelName("test-model");
        aiRequest.setTemperature(Double.valueOf(0.7f));
        aiRequest.setPrompt("情绪分析提示词");
        aiRequest.setMaxResponseLength(100);
        when(redisService.get(anyString())).thenReturn(JSONUtil.toJsonStr(aiRequest));

        EmotionResponseDTO emotionResponse = new EmotionResponseDTO();
        emotionResponse.setStart("积极");
        emotionResponse.setEnd("满意");
        
        // Mock testChat response
        try (MockedConstruction<LlmRecordServiceV2Impl> mockedService = mockConstruction(LlmRecordServiceV2Impl.class, (mock, context) -> {
            AiResponse aiResponse = AiResponse.builder().content(JSONUtil.toJsonStr(emotionResponse)).build();
            when(mock.testChat(any())).thenReturn(aiResponse);
        })) {
            
            // When

            // Then
            verify(llmRecordMapper).update(any(LambdaUpdateWrapper.class));
        }
    }

    /**
     * 测试 performTopicSummary 方法正常情况
     */
    @Test
    public void testPerformTopicSummary_Normal() {
        // Given
        String requestId = "test-request-id";
        List<LlmRecord> records = new ArrayList<>();
        LlmRecord record = new LlmRecord();
        record.setUserInput("客户话题内容");
        records.add(record);
        when(llmRecordMapper.selectByRequestId(anyString(), any(), any())).thenReturn(records);

        TopicSummaryDTO topicSummary = new TopicSummaryDTO();
        topicSummary.setTotalEmotion("积极");
        try (MockedStatic<BaiLianUtils> mockedBaiLian = mockStatic(BaiLianUtils.class)) {
            mockedBaiLian.when(() -> BaiLianUtils.callForObject(anyString(), anyString(), anyString(), anyString(), any()))
                    .thenReturn(topicSummary);
        }
    }

    /**
     * 测试 performTagExtraction 方法正常情况
     */
    @Test
    public void testPerformTagExtraction_Normal() {
        // Given
        LlmRecord emotion = new LlmRecord();
        emotion.setId(1L);
        emotion.setUserInput("客户标签内容");

        TagAnalysisDTO tagResult = new TagAnalysisDTO();
        List<TagAnalysisDTO.TopicSummary> summaries = new ArrayList<>();
        TagAnalysisDTO.TopicSummary summary = new TagAnalysisDTO.TopicSummary();
        summary.setQuestion("产品问题");
        summary.setAnswer("产品答案");
        summaries.add(summary);
        tagResult.setCustomerQuestionSummaries(summaries);

        try (MockedStatic<BaiLianUtils> mockedBaiLian = mockStatic(BaiLianUtils.class)) {
            mockedBaiLian.when(() -> BaiLianUtils.callForObject(anyString(), anyString(), anyString(), anyString(), any()))
                    .thenReturn(tagResult);

            // Then
            verify(llmRecordMapper).update(any(LambdaUpdateWrapper.class));
        }
    }
}
