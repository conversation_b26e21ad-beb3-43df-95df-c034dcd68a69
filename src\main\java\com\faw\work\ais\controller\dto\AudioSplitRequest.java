package com.faw.work.ais.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 音频切分请求数据传输对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "音频切分请求")
public class AudioSplitRequest {

    /**
     * 时间戳列表
     */
    @Schema(description = "时间戳列表，用于指定切分的时间段")
    @Size(min = 1, max = 10, message = "时间戳列表长度必须在1-10之间")
    @Valid
    private List<TimeStampDTO> timeStamps;

    @Schema(description = "音频文件URL", example = "https://example.com/audio.m4a")
    private String audioUrl;
}
