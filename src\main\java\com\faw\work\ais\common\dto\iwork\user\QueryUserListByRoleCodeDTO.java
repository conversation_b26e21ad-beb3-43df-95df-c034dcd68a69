package com.faw.work.ais.common.dto.iwork.user;



import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "iwork通过用户角色code查询用户列表参数实体类")
public class QueryUserListByRoleCodeDTO {

    @Schema(description ="用户角色编码")
    @NotBlank(message = "角色编码不能为空")
    private String code;

    @Schema(description ="用户角色id")
    private String id;

    @Schema(description ="用户角色名称")
    private String name;

}
