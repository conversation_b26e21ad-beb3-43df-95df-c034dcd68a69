package com.faw.work.ais.aic.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * 分片标签响应DTO
 * 
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SliceTagResponseDTO {
    
    /**
     * 需求标签列表
     */
    private List<DemandTag> demandTags;
    
    /**
     * 需求标签
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DemandTag {
        /**
         * 一级标签
         */
        private String level1;
        
        /**
         * 二级标签
         */
        private String level2;
        
        /**
         * 三级标签
         */
        private String level3;
    }
}
