package com.faw.work.ais.controller;

import com.alibaba.fastjson2.JSON;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.entity.dto.PracticePair;
import com.faw.work.ais.service.PracticeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/practice")
@Tag(name = "数字人对练", description = "内容审核相关接口")
public class PracticeController {

    @Autowired
    private PracticeService practiceService;

    @PostMapping("/score")
    @Operation(summary = "数字人对练评分", description = "[author:10207439]")
    public Response<Integer> commentSummary(@RequestBody @Valid PracticePair pairs) {
        log.info("接收到数字人对练评分请求：{}", JSON.toJSONString(pairs));

        Integer score = practiceService.practiceScore(pairs);
        return Response.success(score);
    }
}
