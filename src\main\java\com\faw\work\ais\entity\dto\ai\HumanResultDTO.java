package com.faw.work.ais.entity.dto.ai;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.wildfly.common.annotation.NotNull;

import java.util.Date;

/**
 * AI的对应的人工审核结果
 * <AUTHOR>
 * @since 2024/7/11
 */
@Schema(description = "AI的对应的人工审核结果")
@Data
public class HumanResultDTO {

    /**
     * 人工审核结果-单次; 0-驳回；1-通过；单次
     */
    @Schema(description = "人工审核结果-单次; 0-驳回；1-通过；单次")
    @NotNull
    private String humanCheckResultSingle;

    /**
     * 人工驳回原因；
     */
    @Schema(description = "人工驳回原因；")
    @NotNull
    private String humanRefuseReason;

    /**
     * 审核单据的批次id
     */
    @Schema(description = "审核单据的批次id")
    @NotNull
    private String batchId;

    /**
     * 调用一次AI审核的唯一id
     */
    @Schema(description = "调用一次AI审核的唯一id")
    @NotNull
    private String traceId;

    /**
     * 业务主键，不唯一
     */
    @Schema(description = "业务主键，不唯一")
    @NotNull
    private String bizId;

    /**
     * 规则名称
     */
    @Schema(description = "规则名称")
    @NotNull
    private String taskType;

    /**
     * 业务类型
     */
    @Schema(description = "业务类型")
    private String bizType;

    /**
     * 人工审核时间/
     */
    @Schema(description = "人工审核时间")
    private String humanCheckTime;

    /**
     * 系统id
     */
    @Schema(description = "系统id")
    @NotNull
    private String systemId;

    /**
     * 人工审核结果-单据维度; 0-驳回；1-通过；按单据维度，多次审核一次失败就为驳回，全部通过算通过；
     */
    @Schema(description = "人工审核结果-单据维度; 0-驳回；1-通过；按单据维度，多次审核一次失败就为驳回，全部通过算通过；")
    @NotNull
    private String humanCheckResultFinal;

}
