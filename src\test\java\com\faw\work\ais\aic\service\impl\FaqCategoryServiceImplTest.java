package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.faw.work.ais.aic.mapper.faq.FaqCategoryMapper;
import com.faw.work.ais.aic.mapper.faq.FaqKnowledgeMapper;
import com.faw.work.ais.aic.mapper.faq.FaqRobotKnowledgeJoinsMapper;
import com.faw.work.ais.aic.mapper.faq.FaqSimilarKnowledgeMapper;
import com.faw.work.ais.aic.model.domain.FaqCategoryPO;
import com.faw.work.ais.aic.model.domain.FaqKnowledgePO;
import com.faw.work.ais.aic.model.domain.FaqRobotKnowledgeJoinsPO;
import com.faw.work.ais.aic.model.domain.FaqSimilarKnowledgePO;
import com.faw.work.ais.aic.model.request.FaqCategoryRequest;
import com.faw.work.ais.aic.model.request.FaqKnowledgeConditionRequest;
import com.faw.work.ais.aic.model.response.FaqCategoryResponse;
import com.faw.work.ais.aic.service.MilvusService;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.common.util.UserThreadLocalUtil;
import com.faw.work.ais.convert.FaqCategoryPOConverter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * FaqCategoryServiceImpl 单元测试
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class FaqCategoryServiceImplTest {

    @InjectMocks
    private FaqCategoryServiceImpl faqCategoryService;

    @Mock
    private FaqCategoryMapper faqCategoryMapper;

    @Mock
    private FaqKnowledgeMapper faqKnowledgeMapper;

    @Mock
    private FaqRobotKnowledgeJoinsMapper faqRobotKnowledgeJoinsMapper;

    @Mock
    private FaqSimilarKnowledgeMapper faqSimilarKnowledgeMapper;

    @Mock
    private FaqCategoryPOConverter faqCategoryPOConverter;

    @Mock
    private MilvusService milvusService;

    @BeforeEach
    void setUp() {
        // 初始化Mockito注解
        MockitoAnnotations.openMocks(this);
    }

    // Test for getCategoryListByName
    @Test
    public void testGetCategoryListByName_Success() {
        // Given
        String categoryName = "测试类目";
        List<FaqCategoryPO> mockList = new ArrayList<>();
        FaqCategoryPO po = new FaqCategoryPO();
        po.setId("1");
        po.setName(categoryName);
        mockList.add(po);

        when(faqCategoryMapper.selectCategoryListByName(categoryName)).thenReturn(mockList);

        // When
        List<FaqCategoryPO> result = faqCategoryService.getCategoryListByName(categoryName);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("1", result.get(0).getId());
        verify(faqCategoryMapper, times(1)).selectCategoryListByName(categoryName);
    }

    @Test
    public void testGetCategoryListByName_EmptyResult() {
        // Given
        String categoryName = "不存在的类目";
        when(faqCategoryMapper.selectCategoryListByName(categoryName)).thenReturn(Collections.emptyList());

        // When
        List<FaqCategoryPO> result = faqCategoryService.getCategoryListByName(categoryName);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(faqCategoryMapper, times(1)).selectCategoryListByName(categoryName);
    }

    // Test for getIdByName
    @Test
    public void testGetIdByName_Success() {
        // Given
        String categoryName = "测试类目";
        FaqCategoryPO po = new FaqCategoryPO();
        po.setId("1");
        po.setName(categoryName);

        // 使用ArgumentCaptor捕获LambdaQueryWrapper参数
        when(faqCategoryService.getOne(any(LambdaQueryWrapper.class))).thenReturn(po);

        // When
        String result = faqCategoryService.getIdByName(categoryName);

        // Then
        assertNotNull(result);
        assertEquals("1", result);
    }

    @Test
    public void testGetIdByName_NotFound() {
        // Given
        String categoryName = "不存在的类目";
        when(faqCategoryService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // When
        String result = faqCategoryService.getIdByName(categoryName);

        // Then
        assertNull(result);
    }

    // Test for createCategory
    @Test
    public void testCreateCategory_Success() {
        // Given
        String categoryName = "新类目";
        String userName = "测试用户";
        try (MockedStatic<UserThreadLocalUtil> mocked = mockStatic(UserThreadLocalUtil.class)) {
            mocked.when(UserThreadLocalUtil::getRealName).thenReturn(userName);

            FaqCategoryPO savedPo = new FaqCategoryPO();
            savedPo.setId("1");
            savedPo.setName(categoryName);
            savedPo.setCreatedBy(userName);
            savedPo.setCreatedAt(LocalDateTime.now());
            savedPo.setUpdatedAt(LocalDateTime.now());

            // When
            String result = faqCategoryService.createCategory(categoryName);

            // Then
            assertNotNull(result);
            assertFalse(result.isEmpty());
        }
    }

    // Test for createFlexibleCategory
    @Test
    public void testCreateFlexibleCategory_NameExists_ThrowsException() {
        // Given
        FaqCategoryRequest request = new FaqCategoryRequest();
        request.setName("已存在类目");
        request.setType("00");

        when(faqCategoryService.count(any(LambdaQueryWrapper.class))).thenReturn(1L);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            faqCategoryService.createFlexibleCategory(request);
        });

        assertEquals("类目名称已存在", exception.getMessage());
        verify(faqCategoryService, never()).save(any(FaqCategoryPO.class));
    }

    @Test
    public void testCreateFlexibleCategory_TypeZero_Success() {
        // Given
        FaqCategoryRequest request = new FaqCategoryRequest();
        request.setName("一级类目");
        request.setType("00");

        when(faqCategoryService.count(any(LambdaQueryWrapper.class))).thenReturn(0L);
        try (MockedStatic<UserThreadLocalUtil> mocked = mockStatic(UserThreadLocalUtil.class)) {
            mocked.when(UserThreadLocalUtil::getCurrentName).thenReturn("testUser");

            // When
            String result = faqCategoryService.createFlexibleCategory(request);

            // Then
            assertNotNull(result);
            assertFalse(result.isEmpty());
            verify(faqCategoryService, times(1)).save(any(FaqCategoryPO.class));
        }
    }

    @Test
    public void testCreateFlexibleCategory_TypeOne_Success() {
        // Given
        FaqCategoryRequest request = new FaqCategoryRequest();
        request.setName("平级类目");
        request.setType("01");
        request.setParentId("parent1");

        when(faqCategoryService.count(any(LambdaQueryWrapper.class))).thenReturn(0L);
        try (MockedStatic<UserThreadLocalUtil> mocked = mockStatic(UserThreadLocalUtil.class)) {
            mocked.when(UserThreadLocalUtil::getCurrentName).thenReturn("testUser");

            // When
            String result = faqCategoryService.createFlexibleCategory(request);

            // Then
            assertNotNull(result);
            assertFalse(result.isEmpty());
            verify(faqCategoryService, times(1)).save(any(FaqCategoryPO.class));
        }
    }

    @Test
    public void testCreateFlexibleCategory_TypeTwo_NoKnowledge_Success() {
        // Given
        FaqCategoryRequest request = new FaqCategoryRequest();
        request.setName("子类目");
        request.setType("02");
        request.setParentId("parent1");

        when(faqCategoryService.count(any(LambdaQueryWrapper.class))).thenReturn(0L);
        when(faqKnowledgeMapper.selectAllByCondition(any(FaqKnowledgeConditionRequest.class)))
                .thenReturn(Collections.emptyList());
        try (MockedStatic<UserThreadLocalUtil> mocked = mockStatic(UserThreadLocalUtil.class)) {
            mocked.when(UserThreadLocalUtil::getCurrentName).thenReturn("testUser");

            // When
            String result = faqCategoryService.createFlexibleCategory(request);

            // Then
            assertNotNull(result);
            assertFalse(result.isEmpty());
            verify(faqCategoryService, times(1)).save(any(FaqCategoryPO.class));
        }
    }

    @Test
    public void testCreateFlexibleCategory_TypeTwo_HasKnowledge_ThrowsException() {
        // Given
        FaqCategoryRequest request = new FaqCategoryRequest();
        request.setName("子类目");
        request.setType("02");
        request.setParentId("parent1");

        when(faqCategoryService.count(any(LambdaQueryWrapper.class))).thenReturn(0L);
        
        List<FaqKnowledgePO> knowledgeList = new ArrayList<>();
        knowledgeList.add(new FaqKnowledgePO());
        when(faqKnowledgeMapper.selectAllByCondition(any(FaqKnowledgeConditionRequest.class)))
                .thenReturn(knowledgeList);

        // When & Then
        BizException exception = assertThrows(BizException.class, () -> {
            faqCategoryService.createFlexibleCategory(request);
        });

        assertEquals("父类目下存在知识，无法创建子类目", exception.getMessage());
        verify(faqCategoryService, never()).save(any(FaqCategoryPO.class));
    }

    // Test for treeCategory
    @Test
    public void testTreeCategory_NoRootCategories_ReturnsEmptyList() {
        // Given
        String env = "test";
        when(faqCategoryMapper.findAllRootCategoryList(env)).thenReturn(Collections.emptyList());

        // When
        List<FaqCategoryResponse> result = faqCategoryService.treeCategory(env);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(faqCategoryMapper, times(1)).findAllRootCategoryList(env);
    }

    @Test
    public void testTreeCategory_WithRootCategories_ReturnsTree() {
        // Given
        String env = "test";
        
        // Mock root categories
        List<FaqCategoryPO> rootCategories = new ArrayList<>();
        FaqCategoryPO root1 = new FaqCategoryPO();
        root1.setId("1");
        root1.setName("根类目1");
        rootCategories.add(root1);
        
        when(faqCategoryMapper.findAllRootCategoryList(env)).thenReturn(rootCategories);
        
        // Mock converter
        FaqCategoryResponse response1 = new FaqCategoryResponse();
        response1.setId("1");
        response1.setName("根类目1");
        when(faqCategoryPOConverter.convert2FaqCategoryResponse(root1)).thenReturn(response1);
        
        // Mock children categories
        when(faqCategoryMapper.findChildrenByParentId("1", env)).thenReturn(Collections.emptyList());

        // When
        List<FaqCategoryResponse> result = faqCategoryService.treeCategory(env);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("1", result.get(0).getId());
        verify(faqCategoryMapper, times(1)).findAllRootCategoryList(env);
        verify(faqCategoryMapper, times(1)).findChildrenByParentId("1", env);
    }

    // Test for deleteAllDataByCategoryId
    @Test
    public void testDeleteAllDataByCategoryId_HasKnowledge_ThrowsException() {
        // Given
        String categoryId = "1";
        
        // Mock category IDs
        List<String> categoryIds = Arrays.asList("1");
        
        // Mock similar knowledge
        List<FaqSimilarKnowledgePO> similarKnowledges = Collections.emptyList();
        when(faqSimilarKnowledgeMapper.selectByCategoryIds(categoryIds)).thenReturn(similarKnowledges);
        
        // Mock faq knowledge with data
        List<FaqKnowledgePO> knowledges = new ArrayList<>();
        FaqKnowledgePO knowledge = new FaqKnowledgePO();
        knowledge.setId("k1");
        knowledges.add(knowledge);
        when(faqKnowledgeMapper.selectByCategoryIds(categoryIds)).thenReturn(knowledges);

        // When & Then
        BizException exception = assertThrows(BizException.class, () -> {
            faqCategoryService.deleteAllDataByCategoryId(categoryId);
        });

        assertEquals("该类目下存在知识库，无法删除", exception.getMessage());
    }

    @Test
    public void testDeleteAllDataByCategoryId_Success() {
        // Given
        String categoryId = "1";
        
        // Mock category IDs
        List<String> categoryIds = Arrays.asList("1");
        List<String> childrenIds = Arrays.asList("2", "3");
        
        // Mock similar knowledge
        List<FaqSimilarKnowledgePO> similarKnowledges = new ArrayList<>();
        FaqSimilarKnowledgePO similarKnowledge = new FaqSimilarKnowledgePO();
        similarKnowledge.setId("s1");
        similarKnowledges.add(similarKnowledge);
        when(faqSimilarKnowledgeMapper.selectByCategoryIds(categoryIds)).thenReturn(similarKnowledges);
        
        // Mock faq knowledge without data
        List<FaqKnowledgePO> knowledges = Collections.emptyList();
        when(faqKnowledgeMapper.selectByCategoryIds(categoryIds)).thenReturn(knowledges);
        
        // Mock robot knowledge joins
        List<FaqRobotKnowledgeJoinsPO> joinPOs = new ArrayList<>();
        FaqRobotKnowledgeJoinsPO joinPO = new FaqRobotKnowledgeJoinsPO();
        joinPO.setId("j1");
        joinPOs.add(joinPO);
        when(faqRobotKnowledgeJoinsMapper.selectByKnowledgeIds(anyList())).thenReturn(joinPOs);
        
        // Mock children categories

        // When
        assertDoesNotThrow(() -> {
            faqCategoryService.deleteAllDataByCategoryId(categoryId);
        });

        // Then
        verify(faqRobotKnowledgeJoinsMapper, times(1)).deleteByKnowledgeIds(anyList());
        verify(faqSimilarKnowledgeMapper, times(1)).deleteBatchIds(anyList());
        verify(faqCategoryService, times(1)).removeByIds(childrenIds);
        verify(faqCategoryService, times(1)).removeById(categoryId);
        verify(milvusService, times(1)).deleteByIds(anyString(), anyList(), any());
    }
}
