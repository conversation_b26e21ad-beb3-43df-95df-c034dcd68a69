package com.faw.work.ais.aic.common.mq;

import com.faw.work.ais.aic.common.enums.MessageStatus;
import com.faw.work.ais.aic.mapper.MessageQueueMapper;
import com.faw.work.ais.aic.model.domain.MessageQueue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MessageProducer {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private MessageQueueMapper messageQueueMapper;

    @Value("${rabbitmq.exchange.name:message-processing-exchange}")
    private String exchangeName;

    @Value("${rabbitmq.routing.key:message-processing}")
    private String routingKey;

    @Value("${rabbitmq.queue.name:message-processing-queue}")
    private String queueName;

    public void sendMessage(String messageContent, String bizType) {
        log.info("开始发送消息: {}", messageContent);
        String messageId = UUID.randomUUID().toString();

        // 保存消息到数据库
        MessageQueue messageQueue = new MessageQueue();
        messageQueue.setBizType(bizType);
        messageQueue.setMessageId(messageId);
        messageQueue.setQueueName(queueName);
        messageQueue.setMessageContent(messageContent);
        messageQueue.setStatus(MessageStatus.UNPROCESSED.getCode());
        messageQueue.setRetryCount(0);
        messageQueue.setCreateTime(LocalDateTime.now());
        messageQueue.setUpdateTime(LocalDateTime.now());

        messageQueueMapper.insert(messageQueue);

        // 发送消息到RabbitMQ
        // rabbitTemplate.convertAndSend(exchangeName, routingKey, messageContent, message -> {
        //     message.getMessageProperties().setMessageId(messageId);
        //     return message;
        // });

        log.info("消息已发送，ID: {}", messageId);
    }
} 