package com.faw.work.ais.entity.vo.ai;

import com.faw.work.ais.common.enums.SystemIdEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "人工返回结果接口响应")
@Data
public class HumanResultVo {

    @Schema(description = "项目")
    private String sysTemName;

    @Schema(description = "系统id")
    private String sysTemId;

    @Schema(description = "业务ID")
    private String bizId;

    @Schema(description = "一整单单据id")
    private String batchId;

    @Schema(description = "单据AI审核状态;0-驳回；1-通过")
    private Integer aiResultFinal;

    @Schema(description = "单据AI审核状态;0-驳回；1-通过")
    private String aiResultFinalStr;

    @Schema(description = "单据人工审核状态；0-驳回；1-通过")
    private Integer humanCheckResultFinal;

    @Schema(description = "单据人工审核状态；0-驳回；1-通过")
    private String humanCheckResultFinalStr;

    @Schema(description = "规则名称")
    private String taskName;

    @Schema(description = "规则code")
    private String taskType;

    @Schema(description = "规则Id")
    private String traceId;

    @Schema(description = "规则AI审核状态;0-驳回；1-通过；")
    private Integer aiResultSingle;

    @Schema(description = "规则AI审核状态;0-驳回；1-通过；")
    private String aiResultSingleStr;

    @Schema(description = "规则人工审核状态；0-驳回；1-通过")
    private Integer humanCheckResultSingle;

    @Schema(description = "规则人工审核状态；0-驳回；1-通过")
    private String humanCheckResultSingleStr;

    @Schema(description = "AI审核原因")
    private String aiExplain;

    @Schema(description = "人工审核原因")
    private String humanRefuseReason;

    @Schema(description = "AI审核时间")
    private String aiResultTime;

    @Schema(description = "人工审核时间")
    private String humanCheckTime;

    public String getAiResultFinalStr() {
        if (this.aiResultFinal != null) {
            return this.aiResultFinal == 0 ? "驳回" : "通过";
        }
        return "";
    }

    public String getHumanCheckResultFinalStr() {
        if (this.humanCheckResultFinal != null) {
            return this.humanCheckResultFinal == 0 ? "驳回" : "通过";
        }
        return "";
    }

    public String getAiResultSingleStr() {
        if (this.aiResultSingle != null) {
            return this.aiResultSingle == 0 ? "驳回" : "通过";
        }
        return "";
    }

    public String getHumanCheckResultSingleStr() {
        if (this.humanCheckResultSingle != null) {
            return this.humanCheckResultSingle == 0 ? "驳回" : "通过";
        }
        return "";
    }

    public String getSysTemName() {
        return SystemIdEnum.getValue(this.sysTemId);
    }

}
