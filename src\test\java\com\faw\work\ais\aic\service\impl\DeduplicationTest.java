package com.faw.work.ais.aic.service.impl;

import org.junit.jupiter.api.Test;

/**
 * 去重逻辑测试
 * 
 * <AUTHOR>
 */
public class DeduplicationTest {

    @Test
    public void testDeduplicationLogic() {
        // 模拟重复内容的测试用例
        String content1 = """
                00:00:32 客户: 是智能电吸门儿。  00:00:34
                00:00:34 邀约专员: 这个只是顶配的，111度电的，然后730续航的，带电吸门的。  00:00:38
                00:00:38 客户: 对。  00:00:39""";

        String content2 = """
                00:00:38 客户: 对。  00:00:39
                00:00:39 邀约专员: 730。对，这个车最高续航就是730。  00:00:42
                00:00:42 邀约专员: 这个车的空间比那个空间要大一些。  00:00:46""";

        String expectedResult = """
                00:00:32 客户: 是智能电吸门儿。  00:00:34
                00:00:34 邀约专员: 这个只是顶配的，111度电的，然后730续航的，带电吸门的。  00:00:38
                00:00:38 客户: 对。  00:00:39
                00:00:39 邀约专员: 730。对，这个车最高续航就是730。  00:00:42
                00:00:42 邀约专员: 这个车的空间比那个空间要大一些。  00:00:46""";

        System.out.println("=== 测试去重逻辑 ===");
        System.out.println("内容1:");
        System.out.println(content1);
        System.out.println("\n内容2:");
        System.out.println(content2);
        System.out.println("\n期望结果:");
        System.out.println(expectedResult);
        
        // 这里可以调用实际的去重方法进行测试
        // 由于方法是private的，这里只是展示测试用例的结构
    }

    @Test
    public void testRemoveDuplicateLines() {
        String contentWithDuplicates = """
                00:00:38 客户: 对。  00:00:39
                00:00:38 客户: 对。  00:00:39
                00:00:39 邀约专员: 730。对，这个车最高续航就是730。  00:00:42
                00:01:04 客户: 那个车现在可以试驾不？  00:01:07
                00:01:04 客户: 那个车现在可以试驾不？  00:01:07""";

        String expectedResult = """
                00:00:38 客户: 对。  00:00:39
                00:00:39 邀约专员: 730。对，这个车最高续航就是730。  00:00:42
                00:01:04 客户: 那个车现在可以试驾不？  00:01:07""";

        System.out.println("=== 测试行去重逻辑 ===");
        System.out.println("原始内容:");
        System.out.println(contentWithDuplicates);
        System.out.println("\n期望结果:");
        System.out.println(expectedResult);
    }
}
