package com.faw.work.ais.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import javax.validation.constraints.Pattern;

/**
 * 时间戳数据传输对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "时间戳信息")
public class TimeStampDTO {

    /**
     * 开始时间，格式为HH:mm:ss
     */
    @Schema(description = "开始时间，格式为HH:mm:ss", example = "00:00:10")
    @NotBlank(message = "开始时间不能为空")
    @Pattern(regexp = "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$", 
             message = "开始时间格式必须为HH:mm:ss")
    private String startTime;

    /**
     * 结束时间，格式为HH:mm:ss
     */
    @Schema(description = "结束时间，格式为HH:mm:ss", example = "00:00:20")
    @NotBlank(message = "结束时间不能为空")
    @Pattern(regexp = "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$", 
             message = "结束时间格式必须为HH:mm:ss")
    private String endTime;
}
