package com.faw.work.ais.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.web.multipart.MultipartFile;

//import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;

/**
 * EasyExcel 工具类
 */
public class ExcelUtil {

    /**
     * 读取Excel文件内容（同步读取）
     *
     * @param filePath 文件路径
     * @param head     数据模型类
     * @return 读取的数据列表
     */
    public static <T> List<T> readSync(String filePath, Class<T> head) {
        return EasyExcel.read(filePath).head(head).sheet().doReadSync();
    }

    /**
     * 读取Excel文件内容（同步读取指定sheet）
     *
     * @param filePath  文件路径
     * @param head      数据模型类
     * @param sheetNo   sheet序号（从0开始）
     * @return 读取的数据列表
     */
    public static <T> List<T> readSync(String filePath, Class<T> head, int sheetNo) {
        return EasyExcel.read(filePath).head(head).sheet(sheetNo).doReadSync();
    }

    /**
     * 读取Excel文件内容（同步读取指定sheet）
     *
     * @param filePath  文件路径
     * @param head      数据模型类
     * @param sheetName sheet名称
     * @return 读取的数据列表
     */
    public static <T> List<T> readSync(String filePath, Class<T> head, String sheetName) {
        return EasyExcel.read(filePath).head(head).sheet(sheetName).doReadSync();
    }

    /**
     * 读取Excel文件内容（异步监听方式）
     *
     * @param filePath 文件路径
     * @param head     数据模型类
     * @param consumer 数据处理回调函数
     */
    public static <T> void readAsync(String filePath, Class<T> head, Consumer<T> consumer) {
        EasyExcel.read(filePath, head, new ExcelListener<T>(consumer)).sheet().doRead();
    }

    /**
     * 从MultipartFile读取Excel
     *
     * @param file     上传的文件
     * @param head     数据模型类
     * @param consumer 数据处理回调函数
     */
    public static <T> void readFromMultipartFile(MultipartFile file, Class<T> head, Consumer<T> consumer) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            EasyExcel.read(inputStream, head, new ExcelListener<T>(consumer)).sheet().doRead();
        }
    }

    /**
     * 读取Excel中所有sheet
     *
     * @param filePath 文件路径
     * @param head     数据模型类
     * @return 包含所有sheet数据的列表
     */
    public static <T> List<List<T>> readAllSheets(String filePath, Class<T> head) {
        ExcelReader excelReader = null;
        try {
            excelReader = EasyExcel.read(filePath).head(head).build();
            List<ReadSheet> sheets = excelReader.excelExecutor().sheetList();
            List<List<T>> result = new ArrayList<>(sheets.size());

            for (ReadSheet sheet : sheets) {
                List<T> sheetData = EasyExcel.read(filePath).head(head).sheet(sheet.getSheetNo()).doReadSync();
                result.add(sheetData);
            }
            return result;
        } finally {
            if (excelReader != null) {
                excelReader.finish();
            }
        }
    }

    /**
     * 写入Excel文件（覆盖写入）
     *
     * @param filePath 文件路径
     * @param head     数据模型类
     * @param data     数据列表
     */
    public static <T> void write(String filePath, Class<T> head, List<T> data) {
        EasyExcel.write(filePath, head)
                .registerWriteHandler(createStyleStrategy()) // 设置样式
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(15)) // 设置列宽
                .sheet("Sheet1")
                .doWrite(data);
    }

    /**
     * 写入Excel文件（指定sheet名称）
     *
     * @param filePath  文件路径
     * @param sheetName sheet名称
     * @param head      数据模型类
     * @param data      数据列表
     */
    public static <T> void write(String filePath, String sheetName, Class<T> head, List<T> data) {
        EasyExcel.write(filePath, head)
                .registerWriteHandler(createStyleStrategy())
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(15))
                .sheet(sheetName)
                .doWrite(data);
    }

    /**
     * 写入多个sheet到Excel文件
     *
     * @param filePath 文件路径
     * @param sheets   sheet配置列表
     */
    public static void writeWithSheets(String filePath, List<ExcelSheet<?>> sheets) {
        ExcelWriter excelWriter = null;
        try {
            excelWriter = EasyExcel.write(filePath).build();
            for (int i = 0; i < sheets.size(); i++) {
                ExcelSheet<?> sheet = sheets.get(i);
                WriteSheet writeSheet = EasyExcel.writerSheet(i, sheet.getSheetName())
                        .head(sheet.getHead())
                        .registerWriteHandler(createStyleStrategy())
                        .registerWriteHandler(new SimpleColumnWidthStyleStrategy(15))
                        .build();
                excelWriter.write(sheet.getData(), writeSheet);
            }
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    /**
     * 追加数据到现有Excel文件
     *
     * @param filePath 文件路径
     * @param head     数据模型类
     * @param data     要追加的数据
     */
    public static <T> void append(String filePath, Class<T> head, List<T> data) {
        // 读取现有数据
        List<T> existingData = readSync(filePath, head);

        // 合并数据
        List<T> allData = new ArrayList<>(existingData);
        allData.addAll(data);

        // 重新写入
        write(filePath, head, allData);
    }

    /**
     * 追加数据到指定sheet
     *
     * @param filePath  文件路径
     * @param sheetName sheet名称
     * @param head      数据模型类
     * @param data      要追加的数据
     */
    public static <T> void append(String filePath, String sheetName, Class<T> head, List<T> data) {
        // 读取现有数据
        List<T> existingData = readSync(filePath, head, sheetName);

        // 合并数据
        List<T> allData = new ArrayList<>(existingData);
        allData.addAll(data);

        // 重新写入
        write(filePath, sheetName, head, allData);
    }

    /**
     * 导出Excel到HTTP响应（用于Web下载）
     *
     * @param response  HTTP响应
     * @param fileName  文件名（不带扩展名）
     * @param sheetName sheet名称
     * @param head      数据模型类
     * @param data      数据列表
     */
    public static <T> void exportToResponse(HttpServletResponse response, String fileName,
                                            String sheetName, Class<T> head, List<T> data) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

        try (OutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, head)
                    .registerWriteHandler(createStyleStrategy())
                    .registerWriteHandler(new SimpleColumnWidthStyleStrategy(15))
                    .sheet(sheetName)
                    .doWrite(data);
        }
    }

    /**
     * 创建样式策略（表头样式+内容样式）
     */
    private static HorizontalCellStyleStrategy createStyleStrategy() {
        // 表头样式
        WriteCellStyle headerStyle = new WriteCellStyle();
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont headerFont = new WriteFont();
        headerFont.setFontHeightInPoints((short) 12);
        headerFont.setBold(true);
        headerStyle.setWriteFont(headerFont);
        headerStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        // 内容样式
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        return new HorizontalCellStyleStrategy(headerStyle, contentStyle);
    }

    /**
     * Excel Sheet 配置类
     *
     * @param <T> 数据类型
     */
    public static class ExcelSheet<T> {
        private String sheetName;
        private Class<T> head;
        private List<T> data;

        public ExcelSheet(String sheetName, Class<T> head, List<T> data) {
            this.sheetName = sheetName;
            this.head = head;
            this.data = data;
        }

        public String getSheetName() {
            return sheetName;
        }

        public Class<T> getHead() {
            return head;
        }

        public List<T> getData() {
            return data;
        }
    }

    /**
     * Excel 监听器（用于异步读取）
     *
     * @param <T> 数据类型
     */
    private static class ExcelListener<T> implements ReadListener<T> {
        private final Consumer<T> consumer;

        public ExcelListener(Consumer<T> consumer) {
            this.consumer = consumer;
        }

        @Override
        public void invoke(T data, com.alibaba.excel.context.AnalysisContext context) {
            consumer.accept(data);
        }

        @Override
        public void doAfterAllAnalysed(com.alibaba.excel.context.AnalysisContext context) {
            // 读取完成后的操作
        }
    }
}
