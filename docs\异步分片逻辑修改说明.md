# 异步分片逻辑修改说明

## 修改概述

根据需求，将原有的同步分片逻辑改为异步处理，使`processConversation`方法只负责记录用户输入数据并发送消息，实际的分片逻辑在消息消费者中执行。

## 修改内容

### 1. LlmRecordServiceImpl.java

#### 1.1 processConversation方法修改
**修改前：**
- 直接执行分片逻辑
- 创建LlmRecord记录并保存到数据库
- 发送MQ消息

**修改后：**
- 只检查请求ID是否重复
- 将用户输入数据序列化为JSON存储到MQ消息的remark字段
- 发送MQ消息并直接返回成功

```java
@Override
@Transactional(rollbackFor = Exception.class)
public void processConversation(ProcessRequest request) {
    try {
        String requestId = request.getRequestId();
        
        // 检查请求ID是否已存在
        List<LlmRecord> llmRecords = llmRecordMapper.selectByRequestId(requestId, null, null);
        if (CollUtil.isNotEmpty(llmRecords)) {
            throw new BizException("请求ID已存在，请勿重复提交");
        }

        // 将用户输入数据序列化为JSON存储到remark字段
        String remarkData = JSONUtil.toJsonStr(request);
        
        // 发送MQ消息，将用户输入数据放在remark字段中
        messageProducer.sendMessage(requestId, MessageTypeEnum.DMS_EMOTION_PRODUCT.getCode(), remarkData);
        
        log.info("成功记录用户输入数据并发送消息，请求ID: {}", requestId);

    } catch (Exception e) {
        log.error("处理请求失败，请求ID: {}", request.getRequestId(), e);
        throw new BizException(e.getMessage());
    }
}
```

#### 1.2 新增performSlicing方法
将原有的分片逻辑抽取为独立的public方法，供消息消费者调用：

```java
@Override
public void performSlicing(ProcessRequest request, String requestId) {
    String conversationContent = request.getUserInput();

    // 先利用StrUtils初步进行分片，然后调用标签分片合并模型，将结果进行合并
    List<String> slices = StrUtils.sliceConversation(conversationContent, "客户", 3, 1);

    if (CollUtil.isEmpty(slices)) {
        throw new BizException("对话内容中没有客户的说话内容，分片为空");
    }

    // 创建情绪分析分片和产品需求分析分片
    // ... 具体分片逻辑
}
```

### 2. LlmRecordService.java

#### 2.1 新增接口方法
在服务接口中添加performSlicing方法声明：

```java
/**
 * 执行分片逻辑，将用户输入的对话内容进行分片并存储到数据库
 *
 * @param request 处理请求对象
 * @param requestId 请求ID
 */
void performSlicing(ProcessRequest request, String requestId);
```

### 3. MessageConsumer.java

#### 3.1 添加导入
```java
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.faw.work.ais.aic.model.request.ProcessRequest;
```

#### 3.2 修改processMessage方法
在处理DMS_EMOTION_PRODUCT类型消息时，先执行分片逻辑：

```java
if (MessageTypeEnum.DMS_EMOTION_PRODUCT.getCode().equals(messageQueue.getBizType())) {
    log.info("开始处理情绪识别模型，ID: {}", messageId);
    
    // 先进行分片处理
    performSlicing(messageQueue);
    
    // 然后执行后续逻辑
    llmRecordService.processSlice(messageQueue.getMessageContent());
}
```

#### 3.3 新增performSlicing方法
```java
private void performSlicing(MessageQueue messageQueue) {
    try {
        // 从remark字段中获取用户输入数据
        String remarkData = messageQueue.getRemark();
        if (StrUtil.isBlank(remarkData)) {
            log.error("消息队列remark字段为空，无法进行分片处理，消息ID: {}", messageQueue.getMessageId());
            throw new RuntimeException("消息队列remark字段为空，无法进行分片处理");
        }

        // 反序列化用户输入数据
        ProcessRequest request = JSONUtil.toBean(remarkData, ProcessRequest.class);
        log.info("开始执行分片逻辑，请求ID: {}", request.getRequestId());

        // 调用LlmRecordService的分片方法
        llmRecordService.performSlicing(request, messageQueue.getMessageContent());
        
        log.info("分片逻辑执行完成，请求ID: {}", request.getRequestId());

    } catch (Exception e) {
        log.error("执行分片逻辑失败，消息ID: {}", messageQueue.getMessageId(), e);
        throw new RuntimeException("执行分片逻辑失败: " + e.getMessage(), e);
    }
}
```

### 4. MessageProducer.java

#### 4.1 新增支持remark参数的sendMessage方法
```java
public void sendMessage(String messageContent, String bizType, String remark) {
    log.info("开始发送消息: {}", messageContent);
    String messageId = UUID.randomUUID().toString();

    // 保存消息到数据库
    MessageQueue messageQueue = new MessageQueue();
    messageQueue.setBizType(bizType);
    messageQueue.setMessageId(messageId);
    messageQueue.setQueueName(queueName);
    messageQueue.setMessageContent(messageContent);
    messageQueue.setStatus(MessageStatus.UNPROCESSED.getCode());
    messageQueue.setRetryCount(0);
    messageQueue.setCreateTime(LocalDateTime.now());
    messageQueue.setUpdateTime(LocalDateTime.now());
    messageQueue.setRemark(remark);

    messageQueueMapper.insert(messageQueue);

    // 发送消息到RabbitMQ
    rabbitTemplate.convertAndSend(exchangeName, routingKey, messageContent, message -> {
        message.getMessageProperties().setMessageId(messageId);
        return message;
    });

    log.info("消息已发送，ID: {}", messageId);
}
```

## 修改后的处理流程

1. **用户请求阶段**：
   - `processConversation`接收用户请求
   - 检查请求ID重复性
   - 将完整的ProcessRequest对象序列化存储到MQ消息的remark字段
   - 发送MQ消息
   - 立即返回成功

2. **异步处理阶段**：
   - `MessageConsumer.processMessage`接收MQ消息
   - 从remark字段反序列化获取原始用户请求数据
   - 调用`performSlicing`执行分片逻辑
   - 继续执行原有的`processSlice`逻辑

## 优势

1. **响应速度提升**：用户请求立即返回，不需要等待分片处理完成
2. **解耦合**：分片逻辑与用户请求处理分离
3. **可扩展性**：为后续添加大模型分片逻辑预留了空间
4. **数据完整性**：用户输入数据完整保存在remark字段中，便于后续处理和调试

## 注意事项

1. 确保MessageQueue表的remark字段有足够的存储空间
2. 分片逻辑中的异常处理需要完善，避免影响整个消息处理流程
3. 后续添加大模型分片逻辑时，可以在`performSlicing`方法中进行扩展
