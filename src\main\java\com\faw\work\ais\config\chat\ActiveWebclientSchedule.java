package com.faw.work.ais.config.chat;

import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * Webclient保活定时任务
 *
 * <AUTHOR>
 * @since 2025-09-08 15:01
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ActiveWebclientSchedule {

    private final ChatClient chatClient;

    private static final String SAMPLE_PROMPT = "请输出数字1";

    @Scheduled(cron = "0 0/1 * * * ?")
    public void activeWebclientSchedule() {
        try {
            chatClient.prompt(SAMPLE_PROMPT).user(SAMPLE_PROMPT).stream()
                    .chatResponse().map(this::afterChat).subscribe();
            log.info("[ActiveWebclientSchedule][activeWebclientSchedule] Executed successfully.");
        } catch (Exception e) {
            log.warn("[ActiveWebclientSchedule][activeWebclientSchedule][exception] e: ", e);
        }
    }

    /**
     * 响应处理
     *
     * @param chatResponse 响应
     * @return 响应结果
     */
    private String afterChat(ChatResponse chatResponse) {
        log.info("[ActiveWebclientSchedule][afterChat][entrance] chatResponse: {}", JSON.toJSONString(chatResponse));
        return chatResponse.getResult().getOutput().getText();
    }


}
