package com.faw.work.ais.entity.dto.bnzxtest;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 国补资源测试DTO
 */
@Schema(description = "国补资源测试DTO")
@Data
public class BnzxTestDTO {

    @Schema(description = "任务类型")
    private String taskType;

    @Schema(description = "身份证件")
    private List<String> ids;

    @Schema(description = "开始 num")
    private Integer beginNum;

    @Schema(description = "结束编号")
    private Integer endNum;

    @Schema(description = "AI 结果")
    private String aiResult;

    @Schema(description = "原始结果")
    private String rawResult;

    @Schema(description = "地位")
    private String status;

}

