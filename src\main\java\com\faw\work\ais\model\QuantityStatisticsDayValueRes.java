package com.faw.work.ais.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
* 数量统计表
* Created  by Mr.hp
* DateTime on 2025-01-13 14:12:35
* <AUTHOR> Mr.hp
* @Schema(title = "数量统计表", description = "数量统计表")
*/
@Data
@NoArgsConstructor
@Schema(description = "数量统计表")
public class QuantityStatisticsDayValueRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 系统ID
    * @Schema(description = "系统ID")
    */
    @Schema(description = "系统ID")
    private String systemId;

    /**
     * 系统ID
     * @Schema(description = "系统ID")
     */
    @Schema(description = "系统ID")
    private String systemName;
    /**
     * 系统ID
     * @Schema(description = "結果")
     */
    @Schema(description = "結果")
    private List<QuantityStatisticsDayValue> quantityStatisticsDayValueList;


}
