package com.faw.work.ais.common.util;

import org.apache.commons.math3.linear.*;

import java.util.Arrays;
import java.util.Map;

/**
 * 增强版向量数学计算工具类
 * 集成Apache Commons Math库并提供便捷的向量/矩阵操作方法
 */
public class VectorMathUtils {

    // 私有构造方法防止实例化
    private VectorMathUtils() {}

    /* ========== 向量转换方法 ========== */

    /**
     * 将double数组转换为RealVector
     */
    public static RealVector toRealVector(double[] array) {
        return new ArrayRealVector(array);
    }

    /**
     * 将RealVector转换为double数组
     */
    public static double[] toArray(RealVector vector) {
        return vector.toArray();
    }

    /**
     * 将二维double数组转换为RealMatrix
     */
    public static RealMatrix toRealMatrix(double[][] array) {
        return new Array2DRowRealMatrix(array, false);
    }

    /* ========== 基本向量操作 ========== */

    /**
     * 向量加法 (使用Apache Commons Math实现)
     */
    public static double[] add(double[] vec1, double[] vec2) {
        return toRealVector(vec1).add(toRealVector(vec2)).toArray();
    }

    /**
     * 向量减法 (使用Apache Commons Math实现)
     */
    public static double[] subtract(double[] vec1, double[] vec2) {
        return toRealVector(vec1).subtract(toRealVector(vec2)).toArray();
    }

    /**
     * 向量点积 (使用Apache Commons Math实现)
     */
    public static double dotProduct(double[] vec1, double[] vec2) {
        return toRealVector(vec1).dotProduct(toRealVector(vec2));
    }

    /**
     * 向量叉积 (3维向量，使用自定义实现)
     */
    public static double[] crossProduct(double[] vec1, double[] vec2) {
        if (vec1.length != 3 || vec2.length != 3) {
            throw new IllegalArgumentException("叉积仅支持3维向量");
        }
        return new double[] {
                vec1[1] * vec2[2] - vec1[2] * vec2[1],
                vec1[2] * vec2[0] - vec1[0] * vec2[2],
                vec1[0] * vec2[1] - vec1[1] * vec2[0]
        };
    }

    /**
     * 向量数乘 (使用Apache Commons Math实现)
     */
    public static double[] scalarMultiply(double[] vector, double scalar) {
        return toRealVector(vector).mapMultiply(scalar).toArray();
    }

    /* ========== 向量属性计算 ========== */

    /**
     * 计算向量范数 (使用Apache Commons Math实现)
     */
    public static double norm(double[] vector) {
        return toRealVector(vector).getNorm();
    }

    /**
     * 计算向量标准化 (使用Apache Commons Math实现)
     */
    public static double[] normalize(double[] vector) {
        RealVector v = toRealVector(vector);
        if (v.getNorm() == 0) {
            throw new ArithmeticException("不能对零向量进行标准化");
        }
        return v.unitVector().toArray();
    }

    /**
     * 计算向量夹角 (使用Apache Commons Math实现)
     */
    public static double angle(double[] vec1, double[] vec2) {
//        return toRealVector(vec1).angle(toRealVector(vec2));
//        // 验证向量长度
//        if (vec1.length != vec2.length) {
//            throw new DimensionMismatchException(v2.length, v1.length);
//        }
//
//        RealVector vec1 = new ArrayRealVector(vec1);
//        RealVector vec2 = new ArrayRealVector(vec2);
//
//        // 检查零向量
//        if (vec1.getNorm() == 0 || vec2.getNorm() == 0) {
//            throw new IllegalArgumentException("零向量没有定义夹角");
//        }

        // 计算并返回夹角
        return Math.acos(toRealVector(vec1).cosine(toRealVector(vec2)));
    }

    /* ========== 相似度计算 ========== */

    /**
     * 计算余弦相似度 (使用Apache Commons Math实现)
     */
    public static double cosineSimilarity(double[] vec1, double[] vec2) {
        return toRealVector(vec1).cosine(toRealVector(vec2));
    }

    /**
     * 计算欧几里得距离 (使用Apache Commons Math实现)
     */
    public static double euclideanDistance(double[] vec1, double[] vec2) {
        return toRealVector(vec1).getDistance(toRealVector(vec2));
    }

    /**
     * 计算曼哈顿距离 (使用Apache Commons Math实现)
     */
    public static double manhattanDistance(double[] vec1, double[] vec2) {
        return toRealVector(vec1).getL1Distance(toRealVector(vec2));
    }

    /* ========== 矩阵操作 ========== */

    /**
     * 矩阵乘法
     */
    public static double[][] matrixMultiply(double[][] matrix1, double[][] matrix2) {
        return toRealMatrix(matrix1).multiply(toRealMatrix(matrix2)).getData();
    }

    /**
     * 矩阵转置
     */
    public static double[][] transpose(double[][] matrix) {
        return toRealMatrix(matrix).transpose().getData();
    }

    /**
     * 计算矩阵行列式
     */
    public static double determinant(double[][] matrix) {
        // 使用LU分解计算行列式
        LUDecomposition luDecomposition = new LUDecomposition(toRealMatrix(matrix));
        return luDecomposition.getDeterminant();
    }

    /**
     * 计算矩阵逆
     */
    public static double[][] inverse(double[][] matrix) {
        DecompositionSolver solver = new SingularValueDecomposition(toRealMatrix(matrix)).getSolver();
        if (!solver.isNonSingular()) {
            throw new IllegalArgumentException("矩阵不可逆");
        }
        return solver.getInverse().getData();
    }

    /**
     * 计算矩阵特征值和特征向量
     */
    public static EigenDecomposition eigenDecomposition(double[][] matrix) {
        return new EigenDecomposition(toRealMatrix(matrix));
    }

    /* ========== 稀疏向量操作 ========== */

    /**
     * 稀疏向量余弦相似度 (优化实现)
     */
    public static double sparseCosineSimilarity(Map<String, Double> vec1, Map<String, Double> vec2) {
        // 选择较小的向量进行遍历以提高效率
        Map<String, Double> smaller = vec1.size() < vec2.size() ? vec1 : vec2;
        Map<String, Double> larger = vec1.size() < vec2.size() ? vec2 : vec1;

        double dotProduct = 0.0;
        for (Map.Entry<String, Double> entry : smaller.entrySet()) {
            if (larger.containsKey(entry.getKey())) {
                dotProduct += entry.getValue() * larger.get(entry.getKey());
            }
        }

        double norm1 = sparseNorm(vec1);
        double norm2 = sparseNorm(vec2);

        if (norm1 == 0 || norm2 == 0) {
            return 0.0;
        }

        return dotProduct / (norm1 * norm2);
    }

    /**
     * 计算稀疏向量范数
     */
    public static double sparseNorm(Map<String, Double> vector) {
        double sum = 0.0;
        for (double value : vector.values()) {
            sum += value * value;
        }
        return Math.sqrt(sum);
    }

    /* ========== 实用方法 ========== */

    /**
     * 检查向量长度是否一致
     */
    private static void checkVectorLength(double[] vec1, double[] vec2) {
        if (vec1.length != vec2.length) {
            throw new IllegalArgumentException("向量长度不一致: " + vec1.length + " != " + vec2.length);
        }
    }

    /**
     * 打印向量
     */
    public static void printVector(double[] vector) {
        System.out.println(Arrays.toString(vector));
    }

    /**
     * 打印矩阵
     */
    public static void printMatrix(double[][] matrix) {
        for (double[] row : matrix) {
            System.out.println(Arrays.toString(row));
        }
    }

    /* ========== 测试用例 ========== */

//    public static void main(String[] args) {
//        // 测试向量操作
//        double[] v1 = {1, 2, 3};
//        double[] v2 = {4, 5, 6};
//
//        System.out.println("向量加法: " + Arrays.toString(add(v1, v2)));
//        System.out.println("向量点积: " + dotProduct(v1, v2));
//        System.out.println("向量范数 v1: " + norm(v1));
//        System.out.println("余弦相似度: " + cosineSimilarity(v1, v2));
//        System.out.println("欧几里得距离: " + euclideanDistance(v1, v2));
//
//        // 测试矩阵操作
//        double[][] m1 = {{1, 2}, {3, 4}};
//        double[][] m2 = {{5, 6}, {7, 8}};
//
//        System.out.println("\n矩阵乘法结果:");
//        printMatrix(matrixMultiply(m1, m2));
//
//        System.out.println("\n矩阵转置:");
//        printMatrix(transpose(m1));
//
//        System.out.println("\n矩阵行列式: " + determinant(m1));
//
//        // 测试稀疏向量
//        Map<String, Double> sv1 = new HashMap<>();
//        sv1.put("apple", 1.0);
//        sv1.put("banana", 2.0);
//
//        Map<String, Double> sv2 = new HashMap<>();
//        sv2.put("banana", 3.0);
//        sv2.put("orange", 1.0);
//
//        System.out.println("\n稀疏向量余弦相似度: " + sparseCosineSimilarity(sv1, sv2));
//    }
}