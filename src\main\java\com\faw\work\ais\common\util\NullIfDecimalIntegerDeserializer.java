package com.faw.work.ais.common.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;

/**
 * null if 十进制整数反序列化器
 *
 * <AUTHOR>
 * @date 2025/09/18
 */
public class NullIfDecimalIntegerDeserializer extends JsonDeserializer<Integer> {

    @Override
    public Integer deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);

        // 如果JSON节点是数字
        if (node.isNumber()) {
            // 如果是整数，就正常反序列化
            if (node.isInt()) {
                return node.asInt();
            }
            // 如果是浮点数（小数），则返回null，忽略这个值
            if (node.isFloatingPointNumber()) {
                return null;
            }
        }
        // 对于非数字类型，也返回null，或者根据你的业务需求进行处理
        return null;
    }
}
