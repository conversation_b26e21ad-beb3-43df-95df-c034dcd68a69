
package com.faw.work.ais.common.vo.roleWorkBench;

import com.fasterxml.jackson.annotation.JsonFormat;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@Schema(description = "调用角色工作台查询实例详情返回值")
public class RoleWorkBenchInstanceDetailVO {

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private Boolean enableUploadFile;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private Integer approveStatus;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String approveUserCode;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String description;

    @Schema(description = "任务完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String remark;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String bizUnitName;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private Boolean enableForceClose;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String rejectReason;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String taskTemplateCode;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private Boolean isFollowTasksDone;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String stdTime;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String roleCode;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private Double overdueDays;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String bizId;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String sendUserName;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String taskTemplateVersionCode;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime overdueTime;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String roleGroup;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private List<Object> fileList;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private List<Object> nextTaskTemplateVersionVo;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String taskFlowInstanceCode;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String taskInstanceCode;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String bizUnitCode;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String url;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String handelSystem;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String approveUserName;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private String position;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private List<Object> preFileList;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private Boolean enableApprove;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private Integer status;

    @Schema(description = "是否允许上传文件（1：是，0：否）")
    private CurrentCloser currentCloser;


    @Data
    @AllArgsConstructor
    public static class CurrentCloser {

        @Schema(description = "是否允许上传文件（1：是，0：否）")
        private Boolean enableUploadFile;

        @Schema(description = "是否允许上传文件（1：是，0：否）")
        private Integer closerType;

        @Schema(description = "是否允许上传文件（1：是，0：否）")
        private String approveUserCode;

        @Schema(description = "是否允许上传文件（1：是，0：否）")
        private Boolean enableCancel;

        @Schema(description = "是否允许上传文件（1：是，0：否）")
        private Boolean enableForceClose;

        @Schema(description = "是否允许上传文件（1：是，0：否）")
        private String eventCode;

        @Schema(description = "是否允许上传文件（1：是，0：否）")
        private String approveUserName;

        @Schema(description = "是否允许上传文件（1：是，0：否）")
        private String closerCode;

        @Schema(description = "是否允许上传文件（1：是，0：否）")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime cancelDeadlineTime;

        @Schema(description = "是否允许上传文件（1：是，0：否）")
        private String taskTemplateVersionCode;

        @Schema(description = "是否允许上传文件（1：是，0：否）")
        private String eventName;

        @Schema(description = "是否允许上传文件（1：是，0：否）")
        private Boolean enableApprove;

        @Schema(description = "是否允许上传文件（1：是，0：否）")
        private Integer cancelTimeLimit;
    }
}
