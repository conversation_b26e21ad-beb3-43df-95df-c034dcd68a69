# 分片合并去重逻辑说明

## 问题描述

在分片合并过程中，由于原始分片之间存在overlap（重叠），合并后会出现重复的对话片段。例如：

**问题示例**：
```
00:00:38 客户: 对。  00:00:39
 00:00:38 客户: 对。  00:00:39    ← 重复行
00:00:39 邀约专员: 730。对，这个车最高续航就是730。  00:00:42
```

**期望结果**：
```
00:00:38 客户: 对。  00:00:39
00:00:39 邀约专员: 730。对，这个车最高续航就是730。  00:00:42
```

## 解决方案

在`mergeSlicesBasedOnTags`方法中添加了三层去重逻辑：

### 1. 合并时去重 (mergeContentWithDeduplication)
在合并两个分片时，智能检测和处理重叠内容。

### 2. 重叠检测 (findOverlapStart)
检测两个内容数组的重叠起始位置。

### 3. 行级去重 (removeDuplicateLines)
移除最终结果中的重复行。

## 核心算法实现

### 1. mergeContentWithDeduplication方法

**功能**：合并两个内容并去除重复部分

```java
private String mergeContentWithDeduplication(String currentContent, String newContent) {
    if (StrUtil.isBlank(currentContent)) {
        return newContent;
    }
    if (StrUtil.isBlank(newContent)) {
        return currentContent;
    }

    // 将内容按行分割
    String[] currentLines = currentContent.split("\n");
    String[] newLines = newContent.split("\n");

    // 找到重叠的部分
    int overlapStart = findOverlapStart(currentLines, newLines);
    
    StringBuilder result = new StringBuilder(currentContent);
    
    if (overlapStart >= 0) {
        // 存在重叠，只添加新内容中非重叠的部分
        for (int i = overlapStart; i < newLines.length; i++) {
            if (!result.toString().contains(newLines[i].trim())) {
                result.append("\n").append(newLines[i]);
            }
        }
    } else {
        // 不存在重叠，直接拼接
        result.append("\n").append(newContent);
    }

    return result.toString();
}
```

**处理逻辑**：
1. 检查输入参数的有效性
2. 按行分割内容
3. 查找重叠部分
4. 只添加非重叠的新内容

### 2. findOverlapStart方法

**功能**：找到两个内容数组的重叠起始位置

```java
private int findOverlapStart(String[] currentLines, String[] newLines) {
    if (currentLines.length == 0 || newLines.length == 0) {
        return -1;
    }

    // 从当前内容的末尾开始，寻找与新内容开头的匹配
    for (int i = Math.max(0, currentLines.length - newLines.length); i < currentLines.length; i++) {
        boolean found = true;
        int matchLength = Math.min(currentLines.length - i, newLines.length);
        
        for (int j = 0; j < matchLength; j++) {
            String currentLine = currentLines[i + j].trim();
            String newLine = newLines[j].trim();
            
            if (!currentLine.equals(newLine)) {
                found = false;
                break;
            }
        }
        
        if (found) {
            return matchLength; // 返回重叠后新内容的起始位置
        }
    }
    
    return -1;
}
```

**算法特点**：
- 从当前内容末尾开始匹配
- 逐行比较内容（去除首尾空格）
- 返回重叠后新内容的起始位置

### 3. removeDuplicateLines方法

**功能**：移除文本中的重复行

```java
private String removeDuplicateLines(String content) {
    if (StrUtil.isBlank(content)) {
        return content;
    }

    String[] lines = content.split("\n");
    List<String> uniqueLines = new ArrayList<>();
    
    for (String line : lines) {
        String trimmedLine = line.trim();
        if (StrUtil.isNotBlank(trimmedLine)) {
            // 检查是否已存在相同的行
            boolean isDuplicate = false;
            for (String existingLine : uniqueLines) {
                if (existingLine.trim().equals(trimmedLine)) {
                    isDuplicate = true;
                    break;
                }
            }
            
            if (!isDuplicate) {
                uniqueLines.add(line);
            }
        }
    }

    return String.join("\n", uniqueLines);
}
```

**处理特点**：
- 保持原始行的格式（包括缩进）
- 基于trim后的内容判断重复
- 过滤空行

## 完整处理流程

### 修改后的mergeSlicesBasedOnTags方法

```java
if (shouldMerge) {
    // 合并分片，处理重复内容
    String mergedContent = mergeContentWithDeduplication(
            currentMergedContent.toString(), slice.getContent());
    currentMergedContent = new StringBuilder(mergedContent);
    
    // 更新当前分片信息（保留非空标签）
    if (!slice.hasEmptyTags()) {
        currentSlice = slice;
    }
} else {
    // 不合并，保存当前合并的内容，开始新的分片
    mergedSlices.add(removeDuplicateLines(currentMergedContent.toString().trim()));
    currentSlice = slice;
    currentMergedContent = new StringBuilder(slice.getContent());
}
```

### 处理步骤

1. **标签分析**：并发调用分片模型获取标签
2. **合并判断**：基于标签交集和空标签规则
3. **智能合并**：使用`mergeContentWithDeduplication`处理重叠
4. **最终去重**：使用`removeDuplicateLines`移除剩余重复行

## 示例场景

### 输入分片
```
分片A: 
00:00:32 客户: 是智能电吸门儿。  00:00:34
00:00:34 邀约专员: 这个只是顶配的，111度电的。  00:00:38
00:00:38 客户: 对。  00:00:39

分片B:
00:00:38 客户: 对。  00:00:39
00:00:39 邀约专员: 730。对，这个车最高续航就是730。  00:00:42
```

### 处理过程
1. **重叠检测**：发现`00:00:38 客户: 对。  00:00:39`重复
2. **智能合并**：只添加分片B中的非重叠部分
3. **最终结果**：生成无重复的完整对话

### 输出结果
```
00:00:32 客户: 是智能电吸门儿。  00:00:34
00:00:34 邀约专员: 这个只是顶配的，111度电的。  00:00:38
00:00:38 客户: 对。  00:00:39
00:00:39 邀约专员: 730。对，这个车最高续航就是730。  00:00:42
```

## 性能考虑

### 时间复杂度
- `findOverlapStart`: O(n*m)，其中n和m是两个内容的行数
- `removeDuplicateLines`: O(n²)，其中n是总行数
- 整体复杂度在可接受范围内，因为单个分片的行数通常不大

### 空间复杂度
- 主要开销是存储分割后的行数组
- 使用StringBuilder减少字符串拼接开销

## 配置调整

### 分片参数调整
```java
// 原来：StrUtils.sliceConversation(conversationContent, "客户", 2, 1);
// 现在：StrUtils.sliceConversation(conversationContent, "客户", 1, 1);
```
- 减少初始分片的重叠程度
- 降低后续去重的复杂度

## 测试验证

创建了专门的测试类`DeduplicationTest`来验证去重逻辑：
- 测试重叠内容的合并
- 测试重复行的移除
- 验证最终输出的正确性

## 总结

通过三层去重逻辑的实现：
- ✅ 解决了分片合并时的重复内容问题
- ✅ 保持了对话的时间顺序和完整性
- ✅ 提高了最终分片的质量和可读性
- ✅ 为后续的情绪分析和产品需求分析提供了更好的输入数据
