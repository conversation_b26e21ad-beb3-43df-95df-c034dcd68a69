package com.faw.work.ais.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
/**
* AI任务结果记录表
* Created  by Mr.hp
* DateTime on 2025-01-13 11:20:13
* <AUTHOR> Mr.hp
* @Schema(title = "AI任务结果记录表", description = "AI任务结果记录表")
*/
@Data
@NoArgsConstructor
@Schema(description = "AiTaskResultNew")
public class AiTaskResultNew implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键ID
    * @Schema(description = "主键ID")
    */
    @Schema(description = "主键ID")
    private Long id;

    /**
    * 系统ID
    * @Schema(description = "系统ID")
    */
    @Schema(description = "系统ID")
    private String systemId;

    /**
    * 任务类型
    * @Schema(description = "任务类型")
    */
    @Schema(description = "任务类型")
    private String taskType;

    /**
    * 规则名称
    * @Schema(description = "规则名称")
    */
    @Schema(description = "规则名称")
    private String taskName;

    /**
    * 任务的状态（0-提交AI审核，1-AI已处理，2-回调成功，3-回调失败）
    * @Schema(description = "任务的状态（0-提交AI审核，1-AI已处理，2-回调成功，3-回调失败）")
    */
    @Schema(description = "任务的状态（0-提交AI审核，1-AI已处理，2-回调成功，3-回调失败）")
    private Integer taskStatus;

    /**
    * 业务类型
    * @Schema(description = "业务类型")
    */
    @Schema(description = "业务类型")
    private String bizType;

    /**
    * 业务主键
    * @Schema(description = "业务主键")
    */
    @Schema(description = "业务主键")
    private String bizId;

    /**
    * 审核任务批次标识
    * @Schema(description = "审核任务批次标识")
    */
    @Schema(description = "审核任务批次标识")
    private String batchId;

    /**
    * 审核任务唯1标识
    * @Schema(description = "审核任务唯1标识")
    */
    @Schema(description = "审核任务唯1标识")
    private String traceId;

    /**
    * AI审核结果（0-不通过，1-通过）
    * @Schema(description = "AI审核结果（0-不通过，1-通过）")
    */
    @Schema(description = "AI审核结果（0-不通过，1-通过）")
    private Integer aiResult;

    /**
     *AI审核结果（false-不通过，true-通过）
     */
    @Schema(description = "AI审核结果（false-不通过，true-通过）")
    private String aiResultStr;

    /**
    * AI审核说明
    * @Schema(description = "AI审核说明")
    */
    @Schema(description = "AI审核说明")
    private String aiExplain;

    /**
    * 创建时间
    * @Schema(description = "创建时间")
    */
    @Schema(description = "创建时间")
    private String createTime;

    /**
    * 更新时间
    * @Schema(description = "更新时间")
    */
    @Schema(description = "更新时间")
    private String updateTime;


    /**
     * 开始创建时间
     */
    @Schema(description = "开始创建时间")
    private String startTime;
    /**
     * 结束创建时间
     */
    @Schema(description = "结束创建时间")
    private String endTime;
    /**
     * 文件原始url列表
     */
    @Schema(description = "文件原始url列表")
    private String fileRawList;

    /**
     * COS文件id
     */
    @Schema(description = "COS文件id")
    private String fileId;
    /**
     * 任务类型
     */
    @Schema(description = "任务类型")
    private String contentType;

}
