package com.faw.work.ais.entity.dto.ai;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * Cos信息
 */
@Data
public class CosInfoDTO {

    @Schema(description = "SecretId")
    private String secretId;

    @Schema(description = "SecretKey")
    private String secretKey;

    @Schema(description = "Bucket名称-APPID")
    private String bucketName;

    @Schema(description = "时区")
    private String reginName;

    @Schema(description = "对象键，cos桶中key可通过这个key获取文件下载地址")
    private String key;

    @Schema(description = "被校验的文件路径")
    private String localFile;

    @Schema(description = "文件md5值")
    private String fileMd5Value;

    @Schema(description = "文件的contentType")
    private String contentType;

    @Schema(description = "cos文件路径")
    private String filePath;

}
