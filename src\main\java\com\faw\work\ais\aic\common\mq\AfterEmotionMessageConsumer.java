package com.faw.work.ais.aic.common.mq;

import cn.hutool.core.util.ObjectUtil;
import com.faw.work.ais.aic.common.enums.MessageStatus;
import com.faw.work.ais.aic.common.enums.MessageTypeEnum;
import com.faw.work.ais.aic.model.domain.MessageQueue;
import com.faw.work.ais.aic.mapper.MessageQueueMapper;
import com.faw.work.ais.aic.service.AfterEmotionService;
import com.faw.work.ais.aic.service.LlmRecordService;
import com.faw.work.ais.aic.service.MessageQueueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 售后接待情绪价值模型消息消费者
 * <AUTHOR>
 */
@Component
@Slf4j
public class AfterEmotionMessageConsumer {

    @Autowired
    private LlmRecordService llmRecordService;

    @Autowired
    private MessageQueueMapper messageQueueMapper;

    @Autowired
    private MessageQueueService messageQueueService;

    @Autowired
    private AfterEmotionService afterEmotionService;

    @Value("${mq.openCleanData:true}")
    private boolean openCleanData;

    @RabbitListener(
            queues = "${rabbitmq.after-emotion.queue.name:after-emotion-processing-queue}",
            concurrency = "${rabbitmq.after-emotion.listener.min-concurrency:3}-${rabbitmq.after-emotion.listener.max-concurrency:5}"
    )
    public void processAfterEmotionMessage(String content, Message message) throws InterruptedException {
        String messageId = message.getMessageProperties().getMessageId();
        log.info("收到售后接待情绪价值模型消息，ID: {}, 内容: {}", messageId, content);

        // 轮询等待数据可用,避免事务还没有提交 导致数据查询不到
        MessageQueue messageQueue = null;
        int retryCount = 0;
        int maxRetries = 5;

        while (messageQueue == null && retryCount < maxRetries) {
            messageQueue = messageQueueMapper.selectByMessageId(messageId);
            if (messageQueue == null) {
                log.info("售后接待情绪价值模型消息未入库，等待500ms后重试，重试次数: {}/{}", retryCount + 1, maxRetries);
                Thread.sleep(500);
                retryCount++;
            }
        }

        if (messageQueue == null) {
            log.error("售后接待情绪价值模型消息在数据库中不存在，ID: {}", messageId);
            return;
        }

        if (ObjectUtil.isNull(messageQueue.getBizType())) {
            log.error("售后接待情绪价值模型消息业务类型为空，ID: {}", messageId);
            return;
        }

        try {
            // 1. 状态校验与更新为处理中（独立事务）
            messageQueueService.updateStatus(messageId, MessageStatus.PROCESSING, null);
            log.info("开始处理售后接待情绪价值模型消息，ID: {}", messageId);
            
            if (MessageTypeEnum.DMS_AFTER_EMOTION.getCode().equals(messageQueue.getBizType())) {
                log.info("开始处理售后接待情绪价值模型，ID: {}", messageId);
                afterEmotionService.processAfterEmotionSlice(messageQueue.getMessageContent());
            }

            // 处理成功后
            messageQueueService.updateStatus(messageId, MessageStatus.PROCESSED, null);

            log.info("售后接待情绪价值模型消息处理成功，ID: {}", messageId);

            // 清理消息队列和分片数据
            if (openCleanData) {
                log.info("售后接待情绪价值模型消息处理成功，开始清理消息，ID: {}", messageId);
                llmRecordService.cleanRequestData(messageId);

            }
        } catch (Exception e) {
            log.error("售后接待情绪价值模型消息处理失败，ID: " + messageId, e);
            messageQueueService.updateStatus(messageId, MessageStatus.FAILED, "处理失败原因: " + e.getMessage());
            // 抛出特定异常使消息重新入队，会进入死信队列,这里不在抛出，因为本地消息表已经记录了信息
            // throw new AmqpRejectAndDontRequeueException("处理失败", e);
        }
    }
}
