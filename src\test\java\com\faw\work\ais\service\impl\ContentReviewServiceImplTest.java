package com.faw.work.ais.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.faw.work.ais.aic.config.MilvusPoolConfig;
import com.faw.work.ais.aic.model.dto.MilvusField;
import com.faw.work.ais.aic.service.EmbeddingService;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.entity.domain.ContentRulePO;
import com.faw.work.ais.entity.dto.ContentReviewResult;
import com.faw.work.ais.entity.dto.ContentSearchRes;
import com.faw.work.ais.entity.dto.ContentSearchResult;
import com.faw.work.ais.entity.request.CommentRequest;
import com.faw.work.ais.entity.request.PostRequest;
import com.faw.work.ais.feign.OpenApiFeignClient;
import com.faw.work.ais.mapper.content.ContentRuleMapper;
import com.faw.work.ais.service.ContentLLMService;
import com.faw.work.ais.service.ContentMilvusService;
import com.faw.work.ais.service.LLMVLService;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ContentReviewServiceImplTest {

    @Mock
    private ContentMilvusService milvusService;

    @Mock
    private EmbeddingService embeddingService;

    @Mock
    private LLMVLService llmVlService;

    @Mock
    private ContentLLMService contentLLMService;

    @Mock
    private OpenApiFeignClient openApiFeignClient;

    @Mock
    private ContentRuleMapper contentRuleMapper;

    // 移除Redis相关的Mock对象
    // @Mock
    // private RedisTemplate<String, Object> redisTemplate;

    // @Mock
    // private ValueOperations<String, Object> valueOperations;

    @InjectMocks
    private ContentReviewServiceImpl contentReviewService;

    // 移除setup方法，不再需要模拟Redis行为
    // @BeforeEach
    // void setUp() {
    //     when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    // }

    // Test for similarContent method
    @Test
    void testSimilarContent_WithEmptyContent_ShouldThrowException() {
        BizException exception = assertThrows(BizException.class, () -> {
            contentReviewService.similarContent("", 1);
        });
        assertEquals("传入的内容为空", exception.getMessage());
    }

    @Test
    void testSimilarContent_WithNullContent_ShouldThrowException() {
        BizException exception = assertThrows(BizException.class, () -> {
            contentReviewService.similarContent(null, 1);
        });
        assertEquals("传入的内容为空", exception.getMessage());
    }

    @Test
    void testSimilarContent_WithValidContentAndEmptyResult_ShouldReturnEmptyContent() throws Exception {
        String content = "test content";
        float[] embedding = {0.1f, 0.2f, 0.3f};
        when(embeddingService.getEmbedding(content)).thenReturn(embedding);
        when(milvusService.searchByEmbedding(anyString(), any(float[].class), anyInt(), anyFloat(), anyString()))
                .thenReturn(new ArrayList<>());

        ContentSearchRes result = contentReviewService.similarContent(content, 1);

        assertNotNull(result);
        assertNull(result.getContent());
        assertArrayEquals(embedding, result.getEmbedding());
    }

    @Test
    void testSimilarContent_WithValidContentAndResults_ShouldReturnFirstResult() throws Exception {
        String content = "test content";
        float[] embedding = {0.1f, 0.2f, 0.3f};
        List<ContentSearchResult> searchResults = new ArrayList<>();
        ContentSearchResult result = new ContentSearchResult();
        result.setContent("similar content");
        searchResults.add(result);

        when(embeddingService.getEmbedding(content)).thenReturn(embedding);
        when(milvusService.searchByEmbedding(anyString(), any(float[].class), anyInt(), anyFloat(), anyString()))
                .thenReturn(searchResults);

        ContentSearchRes searchRes = contentReviewService.similarContent(content, 1);

        assertNotNull(searchRes);
        assertEquals("similar content", searchRes.getContent());
        assertArrayEquals(embedding, searchRes.getEmbedding());
    }

    @Test
    void testSimilarContent_WithException_ShouldThrowBizException() throws Exception {
        String content = "test content";
        when(embeddingService.getEmbedding(content)).thenThrow(new RuntimeException("Embedding error"));

        BizException exception = assertThrows(BizException.class, () -> {
            contentReviewService.similarContent(content, 1);
        });
        assertTrue(exception.getMessage().contains("搜索失败"));
    }

    // Test for storeEmbedding method
    @Test
    void testStoreEmbedding_WithValidData_ShouldStoreSuccessfully() throws Exception {
        String content = "test content";
        Integer totalScore = 80;
        float[] embedding = {0.1f, 0.2f, 0.3f};

        contentReviewService.storeEmbedding(content, totalScore, embedding);

        verify(milvusService).storeContentEmbedding(
                eq(MilvusPoolConfig.POST_COLLECTION_NAME),
                isNull(),
                anyList(),
                eq(embedding)
        );
    }

    @Test
    void testStoreEmbedding_WithException_ShouldThrowBizException() throws Exception {
        String content = "test content";
        Integer totalScore = 80;
        float[] embedding = {0.1f, 0.2f, 0.3f};

        doThrow(new RuntimeException("Storage error"))
                .when(milvusService)
                .storeContentEmbedding(anyString(), any(), anyList(), any(float[].class));

        BizException exception = assertThrows(BizException.class, () -> {
            contentReviewService.storeEmbedding(content, totalScore, embedding);
        });
        assertTrue(exception.getMessage().contains("存储失败"));
    }

    // Test for commentScore method
    @Test
    void testCommentScore_WithValidRequest_ShouldReturnScores() throws Exception {
        CommentRequest request = new CommentRequest();
        request.setContent("test comment");
        request.setTopic("test topic");
        float[] embedding = {0.1f, 0.2f, 0.3f};

        String llmResponse = "{\"quality\":{\"score\":85,\"reason\":\"Good quality\"}}";
        when(llmVlService.commentScore(anyString(), anyString())).thenReturn(llmResponse);

        List<ContentReviewResult> results = contentReviewService.commentScore(request, embedding);

        assertNotNull(results);
        assertEquals(2, results.size()); // quality + totalScore
        verify(milvusService).storeContentEmbedding(
                eq(MilvusPoolConfig.COMMENT_COLLECTION_NAME),
                isNull(),
                anyList(),
                eq(embedding)
        );
    }

    @Test
    void testCommentScore_WithLowScore_ShouldNotStore() throws Exception {
        CommentRequest request = new CommentRequest();
        request.setContent("test comment");
        request.setTopic("test topic");
        float[] embedding = {0.1f, 0.2f, 0.3f};

        String llmResponse = "{\"quality\":{\"score\":70,\"reason\":\"Average quality\"}}";
        when(llmVlService.commentScore(anyString(), anyString())).thenReturn(llmResponse);

        List<ContentReviewResult> results = contentReviewService.commentScore(request, embedding);

        assertNotNull(results);
        assertEquals(2, results.size());
        verify(milvusService, never()).storeContentEmbedding(
                eq(MilvusPoolConfig.COMMENT_COLLECTION_NAME),
                any(),
                anyList(),
                any(float[].class)
        );
    }

    @Test
    void testCommentScore_WithException_ShouldThrowBizException() throws Exception {
        CommentRequest request = new CommentRequest();
        request.setContent("test comment");
        request.setTopic("test topic");
        float[] embedding = {0.1f, 0.2f, 0.3f};

        when(llmVlService.commentScore(anyString(), anyString()))
                .thenThrow(new RuntimeException("LLM error"));

        BizException exception = assertThrows(BizException.class, () -> {
            contentReviewService.commentScore(request, embedding);
        });
        assertTrue(exception.getMessage().contains("搜索失败"));
    }

    // Test for commentReview method
    @Test
    void testCommentReview_WithValidRequest_ShouldReturnResult() throws Exception {
        CommentRequest request = new CommentRequest();
        request.setContent("test comment");
        request.setTopic("test topic");

        String llmResponse = "{\"quality\":{\"score\":85,\"reason\":\"Good quality\"}}";
        when(llmVlService.commentScore(anyString(), anyString())).thenReturn(llmResponse);

        JSONObject result = contentReviewService.commentReview(request, 3);

        assertNotNull(result);
        assertTrue(result.containsKey("quality"));
        assertTrue(result.containsKey("totalScore"));
    }

    @Test
    void testCommentReview_WithRetry_ShouldRetryOnFailure() throws Exception {
        CommentRequest request = new CommentRequest();
        request.setContent("test comment");
        request.setTopic("test topic");

        // First two attempts fail, third succeeds
        when(llmVlService.commentScore(anyString(), anyString()))
                .thenThrow(new RuntimeException("Attempt 1 failed"))
                .thenThrow(new RuntimeException("Attempt 2 failed"))
                .thenReturn("{\"quality\":{\"score\":85,\"reason\":\"Good quality\"}}");

        JSONObject result = contentReviewService.commentReview(request, 3);

        assertNotNull(result);
        verify(llmVlService, times(3)).commentScore(anyString(), anyString());
    }

    @Test
    void testCommentReview_WithAllRetriesFailed_ShouldThrowException() throws Exception {
        CommentRequest request = new CommentRequest();
        request.setContent("test comment");
        request.setTopic("test topic");

        when(llmVlService.commentScore(anyString(), anyString()))
                .thenThrow(new RuntimeException("All attempts failed"));

        BizException exception = assertThrows(BizException.class, () -> {
            contentReviewService.commentReview(request, 3);
        });
        assertTrue(exception.getMessage().contains("评分失败"));
    }

    // Test for commentReview_v1 method
    @Test
    void testCommentReviewV1_WithoutCachedRule_ShouldQueryDatabase() throws Exception {
        CommentRequest request = new CommentRequest();
        request.setContent("test comment");
        request.setTopic("test topic");

        // 模拟从数据库获取规则
        ContentRulePO rulePO = new ContentRulePO();
        rulePO.setRule("{\"quality\":{\"score\":100,\"reason\":\"Excellent\"}}");
        when(contentRuleMapper.selectOne(any(QueryWrapper.class))).thenReturn(rulePO);

        String llmResponse = "{\"quality\":{\"score\":85,\"reason\":\"Good quality\"}}";
        when(contentLLMService.commentScore(anyString(), anyString(), anyList())).thenReturn(llmResponse);

        JSONObject result = contentReviewService.commentReview_v1(request);

        assertNotNull(result);
        // 验证是否调用了数据库查询
        verify(contentRuleMapper).selectOne(any(QueryWrapper.class));
        // 验证LLM服务是否被调用
        verify(contentLLMService).commentScore(anyString(), anyString(), anyList());
        // 移除对redisTemplate的验证
        // verify(valueOperations).set(eq("Content:commentRule"), anyString(), anyLong(), any());
    }

    @Test
    void testCommentReviewV1_WithoutRule_ShouldThrowException() throws Exception {
        CommentRequest request = new CommentRequest();
        request.setContent("test comment");
        request.setTopic("test topic");

        // 模拟数据库查询不到规则
        when(contentRuleMapper.selectOne(any(QueryWrapper.class))).thenReturn(null);

        BizException exception = assertThrows(BizException.class, () -> {
            contentReviewService.commentReview_v1(request);
        });
        assertEquals("没有已开启的规则", exception.getMessage());
        // 验证数据库查询是否被调用
        verify(contentRuleMapper).selectOne(any(QueryWrapper.class));
    }

    // 移除 testCommentReviewV1_WithCachedRule_ShouldUseCache()
    // 这个测试用例依赖于模拟Redis缓存，在移除Redis依赖后，这个测试用例将不再适用。

    // Test for convertToList method
    @Test
    void testConvertToList_WithValidJson_ShouldConvertToList() {
        String ruleJson = "{\"quality\":{\"score\":85,\"reason\":\"Good quality\"}}";

        List<ContentReviewResult> results = contentReviewService.convertToList(ruleJson);

        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals("quality", results.get(0).getType());
        assertEquals(85, results.get(0).getScore());
        assertEquals("Good quality", results.get(0).getReason());
    }

    // Test for urlProcessor method
    @Test
    void testUrlProcessor_WithValidUrls_ShouldProcessUrls() {
        List<String> urls = Arrays.asList(
                "http://example.com/image1.jpg",
                "http://example.com/image2.jpg?param=value"
        );

    }

    // Test for jsonParse method
    @Test
    void testJsonParse_WithValidJson_ShouldParseAndCompact() {
        String originalJson = "{\n  \"key\": \"value\"\n}";

    }

    @Test
    void testJsonParse_WithInvalidJson_ShouldThrowException() {
        String invalidJson = "{invalid json}";

    }
}