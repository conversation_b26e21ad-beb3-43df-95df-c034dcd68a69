package com.faw.work.ais.common.dto.iwork.bpm;


import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Schema(description = "启动过程 BO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "bpm工作流启动实体参数")

public class StartProcessBO {

    @Schema(description = "租户限制 ID")
    private String tenantLimitId;
    @Schema(description = "造物主")
    private String creator;
    @Schema(description = "应用源")
    private String appsource;
    @Schema(description = "模板名称")
    private String templateName;
    @Schema(description = "类别")
    private String category;
    @Schema(description = "业务密钥")
    private String businessKey;
    @Schema(description = "系统代码")
    private String sysCode;
    @Schema(description = "程序代码")
    private String procCode;
    @Schema(description = "Proc Inst 名称")
    private String procInstName;
    @Schema(description = "条件")
    private Object conditions;
    @Schema(description = "分配者")
    private Object assignactors;
    @Schema(description = "商业模型")
    private BizModel bizModel;

    @Schema(description = "商业模型")
    public static class BizModel{
        @Schema(description = "应用项目")
        private Object applyItem;
        @Schema(description = "应用标头")
        private Map<String, String> applyHeader;

        public BizModel() {

        }

        public Object getApplyItem() {
            return applyItem;
        }

        public void setApplyItem(Object applyItem) {
            this.applyItem = applyItem;
        }

        public Object getApplyHeader() {
            return applyHeader;
        }

        public void setApplyHeader(Map<String, String> applyHeader) {
            this.applyHeader = applyHeader;
        }
    }
}
