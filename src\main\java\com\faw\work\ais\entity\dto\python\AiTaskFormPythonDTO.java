package com.faw.work.ais.entity.dto.python;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.wildfly.common.annotation.NotNull;

/**
 * Python服务AiTask入参
 */
@Schema(description = "Python服务AiTask入参")
@Data
public class AiTaskFormPythonDTO {

    @Schema(description = "文件 ID")
    @NotNull
    private String fileId;

    @Schema(description = "任务结果")
    @NotNull
    private String taskResult;

}
