package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * ServiceInspectionScoreInfoRes
 * Description:
 * Version:
 *
 * <AUTHOR>
 * @date 2024/9/18.
 */
@Data
@Schema(description = "服务质检评分结果响应DTO")
public class ServiceInspectionScoreInfoRes {

    @Schema(description = "总得分", example = "85.5")
    private BigDecimal totalScore;

    @Schema(description = "得分项明细")
    private List<ServiceInspectionScoreInfoDTO> serviceInspectionScoreInfoList;

}