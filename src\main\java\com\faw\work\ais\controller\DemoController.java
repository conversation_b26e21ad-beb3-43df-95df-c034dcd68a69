package com.faw.work.ais.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.faw.work.ais.aic.common.util.FileDownloadService;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.controller.dto.AudioSplitRequest;
import com.faw.work.ais.controller.dto.AudioSplitResponse;
import com.faw.work.ais.controller.dto.TimeStampDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ws.schild.jave.Encoder;
import ws.schild.jave.EncoderException;
import ws.schild.jave.MultimediaObject;
import ws.schild.jave.encode.AudioAttributes;
import ws.schild.jave.encode.EncodingAttributes;
import ws.schild.jave.info.AudioInfo;
import ws.schild.jave.info.MultimediaInfo;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.*;
import java.util.stream.Stream;

/**
 * 演示控制器 - 优化版本
 *
 * <AUTHOR>
 */
@Tag(name = "演示控制器", description = "演示功能相关接口")
@RestController
@Slf4j
@RequestMapping("/demo")
public class DemoController {

    private static final String SOURCE_AUDIO_FILE = "语音切分.m4a";
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final int MAX_CONCURRENT_TASKS = 5;

    private final Semaphore processingPermits = new Semaphore(MAX_CONCURRENT_TASKS);

    private final Set<File> tempFiles = ConcurrentHashMap.newKeySet();

    @Autowired
    private FileDownloadService fileDownloadService;


    private final ExecutorService audioProcessingExecutor =
            Executors.newFixedThreadPool(MAX_CONCURRENT_TASKS, r -> {
                Thread t = new Thread(r, "audio-processing-" + System.currentTimeMillis());
                t.setDaemon(true);
                return t;
            });


    /**
     * 切分语音片段接口 - 优化版本
     *
     * @param request 切分请求
     * @return 切分结果
     */
    @Operation(summary = "切分语音片段", description = "[author:wangxin213]")
    @PostMapping("/split-audio")
    public Response<AudioSplitResponse> splitAudio(@RequestBody AudioSplitRequest request) {
        log.info("接收到语音切分请求: {}", JSONUtil.toJsonStr(request));


        File tempSourceFile = null;
        Path outputDir;
        boolean permitAcquired = false;

        try {
            // 获取处理许可证（限流）
            permitAcquired = processingPermits.tryAcquire(30, TimeUnit.SECONDS);
            if (!permitAcquired) {
                throw new RuntimeException("系统繁忙，请稍后重试");
            }

            // 1. 参数校验
            validateRequest(request);

            // 2. 准备源音频文件
            tempSourceFile = prepareSourceFile();

            // 3. 获取音频信息
            MultimediaInfo audioInfo = getAudioInfo(tempSourceFile);
            log.info("音频文件信息: 时长={}ms, 格式={}",
                    audioInfo.getDuration(), audioInfo.getFormat());

            // 4. 创建输出目录
            outputDir = createOutputDirectory();

            // 5. 执行音频切分
            List<String> outputFiles = processAudioSplitting(tempSourceFile, outputDir, request, audioInfo);

            // 6. 构建响应
            AudioSplitResponse response = buildResponse(outputDir, outputFiles);

            log.info("语音切分完成: {}", JSONUtil.toJsonStr(response));
            return Response.success(response);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("语音切分被中断", e);
            throw new RuntimeException("处理被中断", e);
        } catch (Exception e) {
            log.error("语音切分失败", e);
            throw new RuntimeException("语音切分失败: " + e.getMessage(), e);
        } finally {
            // 确保资源清理
            cleanupResources(tempSourceFile);
            if (permitAcquired) {
                processingPermits.release();
            }
        }
    }


    /**
     * 验证请求参数
     */
    private void validateRequest(AudioSplitRequest request) {
        if (CollUtil.isEmpty(request.getTimeStamps())) {
            throw new IllegalArgumentException("时间戳列表不能为空");
        }

        if (request.getTimeStamps().size() > 20) {
            throw new IllegalArgumentException("单次请求切分片段不能超过20个");
        }

        // 验证时间戳格式
        for (TimeStampDTO timeStamp : request.getTimeStamps()) {
            try {
                LocalTime.parse(timeStamp.getStartTime(), TIME_FORMATTER);
                LocalTime.parse(timeStamp.getEndTime(), TIME_FORMATTER);
            } catch (Exception e) {
                throw new IllegalArgumentException("时间戳格式错误: " + e.getMessage());
            }
        }
    }

    /**
     * 准备源音频文件（优化版本）
     */
    private File prepareSourceFile() throws IOException {
        ClassPathResource resource = new ClassPathResource(SOURCE_AUDIO_FILE);
        if (!resource.exists()) {
            throw new IllegalArgumentException("源音频文件不存在: " + SOURCE_AUDIO_FILE);
        }

        // 创建临时文件
        File tempFile = File.createTempFile("source_audio_", ".m4a");
        tempFiles.add(tempFile); // 追踪临时文件

        // 复制文件内容
        try (InputStream inputStream = resource.getInputStream()) {
            Files.copy(inputStream, tempFile.toPath(),
                    java.nio.file.StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            // 清理失败的临时文件
            cleanupTempFile(tempFile);
            throw e;
        }

        log.info("准备源音频文件: {} -> {}", SOURCE_AUDIO_FILE, tempFile.getAbsolutePath());
        return tempFile;
    }

    /**
     * 创建输出目录
     */
    private Path createOutputDirectory() throws IOException {
        String outputDirName = DateUtil.format(new Date(), "yyyyMMdd_HHmmss") + "_" + System.currentTimeMillis();
        Path outputDir = Paths.get("src/main/resources", outputDirName);
        Files.createDirectories(outputDir);
        log.info("创建输出目录: {}", outputDir.toAbsolutePath());
        return outputDir;
    }

    /**
     * 获取音频文件信息（带资源管理）
     */
    private MultimediaInfo getAudioInfo(File audioFile) throws EncoderException {
        MultimediaObject multimediaObject = new MultimediaObject(audioFile);
        MultimediaInfo info = multimediaObject.getInfo();

        if (info.getAudio() == null) {
            throw new IllegalArgumentException("文件不包含音频流");
        }

        return info;
    }

    /**
     * 处理音频切分（并发优化版本）
     */
    private List<String> processAudioSplitting(File tempSourceFile, Path outputDir,
                                               AudioSplitRequest request, MultimediaInfo audioInfo) throws Exception {

        List<String> outputFiles = new ArrayList<>();
        List<Future<String>> futures = new ArrayList<>();

        // 提交所有切分任务到线程池
        for (int i = 0; i < request.getTimeStamps().size(); i++) {
            final TimeStampDTO timeStamp = request.getTimeStamps().get(i);
            final String outputFileName = String.format("segment_%d.m4a", i + 1);
            final File outputFile = outputDir.resolve(outputFileName).toFile();

            Future<String> future = audioProcessingExecutor.submit(() -> {
                try {
                    splitAudioSegmentWithFfmpeg(tempSourceFile, outputFile, timeStamp, audioInfo);
                    log.info("成功切分音频片段: {} -> {}", timeStamp, outputFileName);
                    return outputFileName;
                } catch (Exception e) {
                    log.error("切分音频片段失败: {}", outputFileName, e);
                    throw new RuntimeException("切分失败: " + outputFileName, e);
                }
            });

            futures.add(future);
        }

        // 等待所有任务完成
        for (Future<String> future : futures) {
            try {
                String fileName = future.get(60, TimeUnit.SECONDS);
                outputFiles.add(fileName);
            } catch (TimeoutException e) {
                log.error("音频切分任务超时");
                future.cancel(true);
                throw new RuntimeException("音频切分超时", e);
            }
        }

        return outputFiles;
    }

    /**
     * 使用FFmpeg切分单个音频片段（优化版本）
     */
    private void splitAudioSegmentWithFfmpeg(File sourceFile, File outputFile,
                                             TimeStampDTO timeStamp, MultimediaInfo audioInfo) {

        Encoder encoder;
        MultimediaObject source;

        try {
            // 解析时间戳
            LocalTime startTime = LocalTime.parse(timeStamp.getStartTime(), TIME_FORMATTER);
            LocalTime endTime = LocalTime.parse(timeStamp.getEndTime(), TIME_FORMATTER);

            // 计算时间（毫秒）
            long startMs = timeToMilliseconds(startTime);
            long endMs = timeToMilliseconds(endTime);
            long durationMs = endMs - startMs;

            if (durationMs <= 0) {
                throw new IllegalArgumentException("结束时间必须大于开始时间");
            }

            // 验证时间范围
            long totalDurationMs = audioInfo.getDuration();
            if (endMs > totalDurationMs) {
                log.warn("结束时间 {}ms 超过音频总时长 {}ms，将调整为最大时长", endMs, totalDurationMs);
                endMs = totalDurationMs;
                durationMs = endMs - startMs;
            }

            log.debug("切分参数: 开始时间={}ms, 持续时间={}ms", startMs, durationMs);

            // 创建编码器和多媒体对象
            encoder = new Encoder();
            source = new MultimediaObject(sourceFile);

            // 设置编码属性
            EncodingAttributes encodingAttributes = new EncodingAttributes();

            // 设置音频属性（保持原始质量）
            AudioInfo sourceAudioInfo = audioInfo.getAudio();
            AudioAttributes audioAttributes = new AudioAttributes();
            audioAttributes.setCodec("aac");
            audioAttributes.setBitRate(sourceAudioInfo.getBitRate());
            audioAttributes.setSamplingRate(sourceAudioInfo.getSamplingRate());
            audioAttributes.setChannels(sourceAudioInfo.getChannels());

            encodingAttributes.setInputFormat(audioInfo.getFormat());
            encodingAttributes.setOutputFormat("mp4");
            encodingAttributes.setAudioAttributes(audioAttributes);

            // 设置切分时间
            encodingAttributes.setOffset((float) startMs / 1000.0f);
            encodingAttributes.setDuration((float) durationMs / 1000.0f);

            // 执行编码（切分）
            encoder.encode(source, outputFile, encodingAttributes);

            log.debug("音频切分成功: {} -> {}, 大小: {} bytes",
                    sourceFile.getName(), outputFile.getName(), outputFile.length());

        } catch (EncoderException e) {
            log.error("音频切分失败: {}", e.getMessage(), e);
            // 清理失败的输出文件
            if (outputFile.exists()) {
                try {
                    Files.delete(outputFile.toPath());
                } catch (IOException ioException) {
                    log.warn("清理失败的输出文件异常: {}", outputFile.getAbsolutePath(), ioException);
                }
            }
            throw new RuntimeException("音频切分失败: " + e.getMessage(), e);
        } finally {
            // 建议垃圾回收
            System.gc();
        }
    }

    /**
     * 构建响应对象
     */
    private AudioSplitResponse buildResponse(Path outputDir, List<String> outputFiles) {
        AudioSplitResponse response = new AudioSplitResponse();
        response.setOutputDirectory(outputDir.getFileName().toString());
        response.setSegmentFiles(outputFiles);
        response.setTotalSegments(outputFiles.size());
        return response;
    }

    /**
     * 清理资源
     */
    private void cleanupResources(File tempSourceFile) {
        if (tempSourceFile != null) {
            cleanupTempFile(tempSourceFile);
        }
    }

    /**
     * 清理单个临时文件
     */
    private void cleanupTempFile(File tempFile) {
        if (tempFile != null && tempFile.exists()) {
            try {
                Files.delete(tempFile.toPath());
                tempFiles.remove(tempFile);
                log.debug("清理临时文件: {}", tempFile.getAbsolutePath());
            } catch (IOException e) {
                log.warn("清理临时文件失败: {}", tempFile.getAbsolutePath(), e);
            }
        }
    }

    /**
     * 将时间转换为毫秒数
     */
    private long timeToMilliseconds(LocalTime time) {
        return (time.getHour() * 3600L + time.getMinute() * 60L + time.getSecond()) * 1000L +
                time.getNano() / 1_000_000L;
    }

    /**
     * 应用关闭时清理资源
     */
    @PreDestroy
    public void destroy() {
        log.info("开始清理音频处理资源...");

        // 关闭线程池
        audioProcessingExecutor.shutdown();
        try {
            if (!audioProcessingExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                audioProcessingExecutor.shutdownNow();
                log.warn("强制关闭音频处理线程池");
            }
        } catch (InterruptedException e) {
            audioProcessingExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }

        // 清理所有临时文件
        tempFiles.forEach(this::cleanupTempFile);
        tempFiles.clear();

        log.info("音频处理资源清理完成");
    }



    /**
     * ----------------------------------------------------从URL下载音频并切分接口-----------------------------------------------
     *
     * @param request 切分请求（包含URL）
     * @return 切分结果
     */
    @Operation(summary = "核心接口：从URL下载音频并切分", description = "[author:wangxin213] 支持从网络URL下载音频文件进行切分")
    @PostMapping("/split-audio-from-url")
    public Response<AudioSplitResponse> splitAudioFromUrl(@RequestBody AudioSplitRequest request) {
        log.info("接收到URL音频切分请求: {}", JSONUtil.toJsonStr(request));


        File downloadedFile = null;
        Path outputDir = null;
        boolean permitAcquired = false;
        List<File> generatedFiles = new ArrayList<>();

        try {
            // 获取处理许可证（限流）
            permitAcquired = processingPermits.tryAcquire(1, TimeUnit.SECONDS);
            if (!permitAcquired) {
                throw new RuntimeException("处理音频繁忙系，请稍后重试");
            }

            // 1. 参数校验
            validateUrlRequest(request);

            // 2. 下载音频文件
            downloadedFile = fileDownloadService.downloadAudioFile(request.getAudioUrl());
            // 追踪临时文件
            tempFiles.add(downloadedFile);

            // 3. 获取音频信息
            MultimediaInfo audioInfo = getAudioInfo(downloadedFile);
            log.info("下载音频文件信息: 时长={}ms, 格式={}",
                    audioInfo.getDuration(), audioInfo.getFormat());

            // 4. 创建输出目录
            outputDir = createOutputDirectory();

            // 5. 执行音频切分
            List<String> outputFiles = processAudioSplitting(downloadedFile, outputDir, request, audioInfo);

            // 6. 收集生成的文件路径（用于后续删除）
            for (String fileName : outputFiles) {
                generatedFiles.add(outputDir.resolve(fileName).toFile());
            }

            // 7. 构建响应
            AudioSplitResponse response = buildResponse(outputDir, outputFiles);

            // 8. 调度文件删除任务
            scheduleFileDeletion(generatedFiles, outputDir);

            log.info("URL音频切分完成: {}", JSONUtil.toJsonStr(response));
            return Response.success(response);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("URL音频切分被中断", e);
            throw new RuntimeException("处理被中断", e);
        } catch (Exception e) {
            log.error("URL音频切分失败", e);
            // 失败时清理已生成的文件
            cleanupGeneratedFiles(generatedFiles);
            if (outputDir != null) {
                cleanupOutputDirectory(outputDir);
            }
            throw new RuntimeException("URL音频切分失败: " + e.getMessage(), e);
        } finally {
            // 确保资源清理
            cleanupResources(downloadedFile);
            if (permitAcquired) {
                processingPermits.release();
            }
        }
    }

    /**
     * 验证URL请求参数
     */
    private void validateUrlRequest(AudioSplitRequest request) {
        if (CollUtil.isEmpty(request.getTimeStamps())) {
            throw new IllegalArgumentException("时间戳列表不能为空");
        }

        if (request.getAudioUrl() == null || request.getAudioUrl().trim().isEmpty()) {
            throw new IllegalArgumentException("音频文件URL不能为空");
        }


        // 验证URL格式
        try {
            new URL(request.getAudioUrl());
        } catch (Exception e) {
            throw new IllegalArgumentException("音频文件URL格式错误: " + e.getMessage());
        }

        // 验证时间戳格式
        for (TimeStampDTO timeStamp : request.getTimeStamps()) {
            try {
                LocalTime.parse(timeStamp.getStartTime(), TIME_FORMATTER);
                LocalTime.parse(timeStamp.getEndTime(), TIME_FORMATTER);
            } catch (Exception e) {
                throw new IllegalArgumentException("时间戳格式错误: " + e.getMessage());
            }
        }
    }

    /**
     * 调度文件删除任务
     */
    private void scheduleFileDeletion(List<File> generatedFiles, Path outputDir) {
        // 延迟1分钟删除文件，给客户端足够时间下载
        ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

        scheduler.schedule(() -> {
            try {
                log.info("开始执行定时删除任务，删除 {} 个文件", generatedFiles.size());

                // 删除生成的文件
                cleanupGeneratedFiles(generatedFiles);

                // 删除输出目录
                cleanupOutputDirectory(outputDir);

                log.info("定时删除任务完成");
            } catch (Exception e) {
                log.error("定时删除任务执行失败", e);
            } finally {
                scheduler.shutdown();
            }
        }, 1, TimeUnit.MINUTES);
    }

    /**
     * 清理生成的文件
     */
    private void cleanupGeneratedFiles(List<File> files) {
        if (files == null || files.isEmpty()) {
            return;
        }

        for (File file : files) {
            if (file != null && file.exists()) {
                try {
                    Files.delete(file.toPath());
                    log.debug("删除生成的文件: {}", file.getAbsolutePath());
                } catch (IOException e) {
                    log.warn("删除生成的文件失败: {}", file.getAbsolutePath(), e);
                }
            }
        }
    }

    /**
     * 清理输出目录
     */
    private void cleanupOutputDirectory(Path outputDir) {
        if (outputDir == null || !Files.exists(outputDir)) {
            return;
        }

        try {
            // 使用try-with-resources确保Stream被正确关闭
            try (Stream<Path> pathStream = Files.walk(outputDir)) {
                pathStream
                        .sorted(java.util.Comparator.reverseOrder())
                        .map(Path::toFile)
                        .forEach(file -> {
                            try {
                                Files.delete(file.toPath());
                            } catch (IOException e) {
                                log.warn("删除文件失败: {}", file.getAbsolutePath(), e);
                            }
                        });
            }

            log.info("清理输出目录完成: {}", outputDir.toAbsolutePath());
        } catch (IOException e) {
            log.error("清理输出目录失败: {}", outputDir.toAbsolutePath(), e);
        }
    }

}
