package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * Milvus集合迁移请求参数
 * <AUTHOR>
 */
@Data
@Schema(description = "Milvus集合迁移请求参数 [author:10200571]")
public class MigrateCollectionRequest {

    /**
     * 源集合名称
     */
    @NotBlank(message = "源集合名称不能为空")
    @Schema(description = "源集合名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sourceCollection;

    /**
     * 目标集合名称
     */
    @NotBlank(message = "目标集合名称不能为空")
    @Schema(description = "目标集合名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String targetCollection;

    /**
     * 源Milvus连接配置
     */
    @Schema(description = "源Milvus连接配置")
    private MilvusConnectionConfig sourceConfig;

    /**
     * 目标Milvus连接配置
     */
    @Schema(description = "目标Milvus连接配置")
    private MilvusConnectionConfig targetConfig;

    /**
     * Milvus连接配置
     */
    @Data
    @Schema(description = "Milvus连接配置 [author:10200571]")
    public static class MilvusConnectionConfig {
        
        /**
         * Milvus主机地址
         */
        @NotBlank(message = "主机地址不能为空")
        @Schema(description = "Milvus主机地址", requiredMode = Schema.RequiredMode.REQUIRED)
        private String host;

        /**
         * Milvus端口号
         */
        @Schema(description = "Milvus端口号", defaultValue = "19530")
        private Integer port = 19530;

        /**
         * 用户名
         */
        @Schema(description = "用户名")
        private String username;

        /**
         * 密码
         */
        @Schema(description = "密码")
        private String password;

        /**
         * 数据库名称
         */
        @Schema(description = "数据库名称", defaultValue = "default")
        private String databaseName = "default";
    }
}
