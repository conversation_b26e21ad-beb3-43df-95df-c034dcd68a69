package com.faw.work.ais.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
/**
* ai技术模型配置表
* Created  by Mr.hp
* DateTime on 2025-05-08 11:02:28
* <AUTHOR> Mr.hp
* @Schema(title = "ai技术模型配置表", description = "ai技术模型配置表")
*/
@Data
@NoArgsConstructor
@Schema(description = "人工智能ai技术模型配置表")
public class AiTechnologyModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键id
    * @Schema(description = "主键id")
    */
    @Schema(description = "主键id")
    private Long id;

    /**
    * 模型编码
    * @Schema(description = "模型编码")
    */
    @Schema(description = "模型编码")
    private String technologyModelCode;

    /**
    * 模型名称
    */
    @Schema(description = "模型名称")
    private String technologyModelName;

    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    private String createTime;

    /**
    * 更新时间
    * @Schema(description = "更新时间")
    */
    @Schema(description = "更新时间")
    private String updateTime;


}
