package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * ServiceInspectionScoreInfoDTO
 * Description:
 * Version:
 *
 * <AUTHOR>
 * @date 2024/9/18.
 */
@Data
@Schema(description = "服务质检评分信息DTO")
public class ServiceInspectionScoreInfoDTO {

    @Schema(description = "质检项标题")
    private String title;

    @Schema(description = "质检项标题")
    private String modelType;

    @Schema(description = "质检项等级 1,2,3", example = "1")
    private Integer level;

    @Schema(description = "规则类型：0 - 销售顾问判断，1 - 顾客关注点", example = "0")
    private Integer ruleType;

    @Schema(description = "质检项描述")
    private String levelDesc;

    @Schema(description = "质检项一级得分", example = "85.5")
    private BigDecimal levelScore;

    @Schema(description = "权重", example = "10")
    private Integer levelWeight;

    @Schema(description = "是否计算得分：0-否，1-是", example = "1")
    private Integer isCalculateScore;

    @Schema(description = "总得分", example = "90.0")
    private BigDecimal totalScore;

    @Schema(description = "子质检项列表")
    private List<ServiceInspectionScoreInfoDTO> child;

    @Schema(description = "命中语句ID列表")
    private List<String> matchedSentenceIds;

    @Schema(description = "父级质检项描述")
    private String parentLevelDesc;
}